# Wholesale Shop Management App - User Guide

## 📱 Welcome to Your Digital Wholesale Assistant

This comprehensive guide will help you master every feature of your wholesale shop management application. The app is designed to replace paper-based systems and streamline your daily business operations.

## 🚀 Getting Started

### First Time Setup

When you first open the app, you'll go through a simple 5-step setup:

1. **Enter Your Name**: This personalizes the app experience
2. **Shop Name**: Used in reports and throughout the app
3. **Language**: Select your preferred language
4. **Country**: Sets regional defaults
5. **Currency**: All monetary values will use this currency
6. **Security Check**: Ensure phone security is enabled

**💡 Tip**: All this information can be changed later in the settings.

### Understanding the Dashboard

Your dashboard provides a quick overview of your business:

- **Today's Sales**: Number of sales and total revenue
- **Outstanding Debts**: Money owed by customers
- **Low Stock Items**: Products that need restocking
- **Recent Activity**: Latest sales and acquisitions
- **Quick Actions**: Fast access to common tasks

## 📦 Product Management

### Adding a New Product

1. Tap **Products** from the main menu
2. Tap the **+** button
3. Fill in the required information:
   - **Product Name**: What you call the product
   - **Photo**: Take or select a photo (optional but recommended)
   - **Salable Individually**: Can you sell single units?
   - **Wholesale Quantity**: How you buy it (e.g., "10 bags per box")
   - **Unit of Measure**: kg, pieces, liters, etc.

4. Add manufacturer details if known
5. Add storage notes (e.g., "Keep refrigerated")
6. Select which suppliers provide this product
7. Tap **Save**

### Managing Your Product Catalog

- **Search**: Use the search bar to quickly find products
- **Edit**: Tap any product to view details, then tap **Edit**
- **Delete**: Long press a product and select **Delete**
- **Filter**: Use filters to show only certain types of products

**🎯 Best Practice**: Always add photos to products - it makes selection much faster during sales.

## 🏢 Supplier Management (Providers)

### Adding a Supplier

1. Go to **Providers** section
2. Tap **+** to add new supplier
3. Enter contact information:
   - **First Name** (required)
   - **Last Name** (optional)
   - **Phone Numbers**: Add multiple if needed
   - **WhatsApp Numbers**: Mark which numbers have WhatsApp
   - **Email, Website, Company Name** (all optional)
   - **Address and Notes**

### Contacting Suppliers

From any supplier's detail page, you can:
- **Call** directly
- **Send SMS**
- **WhatsApp Call** or **Message**
- **Email** (if email app is installed)
- **Export Contact** to your phone's contact list

**💡 Tip**: Mark WhatsApp numbers for easy international communication.

## 👥 Customer Management (Clients)

### Adding Customers

1. Navigate to **Clients**
2. Tap **+** for new customer
3. Enter:
   - **First Name** (required)
   - **Last Name** (optional)
   - **Phone Number** (required)
   - **Notes** (optional)

### Customer Features

- **Purchase History**: See all past purchases
- **Contact Actions**: Call or text customers directly
- **Debt Tracking**: Automatic tracking of unpaid sales
- **Customer Statistics**: Total purchases, average order value

## 💰 Recording Sales

### Creating a Sale

1. Go to **Sales** section
2. Tap **+** to create new sale
3. **Select Customer** (optional but recommended for tracking)
4. **Add Products**:
   - Tap **Add Product**
   - Select from your catalog
   - Enter quantity and adjust price if needed
   - Tap **Add to Sale**
5. **Receipt Status**: Mark if receipt was issued
6. **Review Total** and tap **Save Sale**

### Managing Sales Records

- **View Sales History**: See all past sales
- **Search Sales**: Find sales by customer, date, or product
- **Edit Sales**: Tap any sale to modify details
- **Delete Sales**: Remove incorrect entries

**🎯 Pro Tip**: Always record sales immediately to maintain accurate inventory tracking.

## 📥 Recording Acquisitions (Inventory)

### Adding New Inventory

1. Go to **Acquisitions**
2. Tap **+** for new acquisition
3. **Select Supplier**
4. **Add Products** you're buying:
   - Choose products from catalog
   - Enter quantities and cost prices
   - Review total cost
5. **Delivery Status**: 
   - Mark as **Delivered** if received
   - Set **Expected Delivery** date if not yet received
6. **Payment Status**: Mark if you've paid the supplier
7. **Save Acquisition**

### Tracking Deliveries

- **Pending Deliveries**: See what's expected
- **Update Status**: Mark deliveries as received
- **Payment Tracking**: Monitor what you owe suppliers

## 💳 Debt Management

### Understanding Debt Tracking

The app automatically creates debt records when:
- A sale is made without immediate payment
- You manually add a debt for a customer

### Managing Customer Debts

1. Go to **Debtors** section
2. See all outstanding debts
3. **Filter debts**:
   - **All**: Every debt record
   - **Unpaid**: Outstanding debts only
   - **Overdue**: Past due date debts

### Adding Manual Debts

1. In **Debtors**, tap **+**
2. **Select Customer**
3. **Enter Amount** owed
4. **Set Due Date** (optional)
5. **Link to Sale** (if applicable)
6. **Add Notes** about the debt

### Debt Collection Features

- **Contact Debtor**: Call or SMS directly from debt record
- **Auto-Generated Messages**: Pre-written debt reminder texts
- **Payment Tracking**: Mark debts as paid when money is received
- **Daily Reminders**: App notifies you at 5 PM about all outstanding debts

**📅 Daily Routine**: Check debts every evening when you get the 5 PM reminder.

## 📊 Reports and Analytics

### Financial Reports

1. Go to **Reports** → **Finance Report**
2. **Select Time Period**:
   - Week, Month, Quarter, Year
   - Custom date range
3. **View Key Metrics**:
   - Total revenue and costs
   - Gross profit and margin
   - Average sale value
   - Top-selling products

### Product Statistics

1. Go to **Reports** → **Product Statistics**
2. **Analyze Product Performance**:
   - Best-selling products
   - Slow-moving inventory
   - Profit margins by product
   - Current stock levels
3. **Filter and Sort**:
   - By category (bestsellers, slow-moving)
   - By performance metrics
   - Search specific products

### Using Reports for Business Decisions

**Weekly Review**:
- Check which products are selling well
- Identify slow-moving items
- Review profit margins

**Monthly Analysis**:
- Calculate total profit/loss
- Plan future inventory purchases
- Evaluate supplier relationships

**Quarterly Planning**:
- Identify seasonal trends
- Adjust product mix
- Set business goals

## 🔔 Notifications and Reminders

### Automatic Debt Reminders

- **Daily at 5 PM**: App shows all customers who owe money
- **Overdue Alerts**: Special highlighting for past-due debts
- **Quick Actions**: Call or message directly from notification

### Managing Notifications

- Enable/disable in phone settings
- Set custom reminder times (if supported by your device)
- Choose notification sounds

## 📱 Daily Workflow Tips

### Morning Routine
1. Check dashboard for overnight changes
2. Review today's expected deliveries
3. Check low stock alerts
4. Plan outreach to overdue debtors

### During Sales
1. Record each sale immediately
2. Take customer contact info for new customers
3. Issue receipts and mark in app
4. Note any special requests or issues

### Evening Routine
1. Review daily sales total
2. Record any late inventory arrivals
3. Check debt reminder notification
4. Plan tomorrow's supplier contacts

### Weekly Tasks
1. Generate weekly financial report
2. Review product statistics
3. Contact overdue debtors
4. Plan inventory restocking

## 🔧 Maintenance and Best Practices

### Data Backup

**Automatic Backup**: App backs up data daily
**Manual Backup**: 
1. Go to Settings
2. Tap **Export Data**
3. Share backup file to cloud storage or email

### Keeping Data Clean

- **Regular Review**: Monthly check for duplicate entries
- **Archive Old Data**: Keep only relevant historical data
- **Verify Contacts**: Ensure customer and supplier info is current

### Performance Optimization

- **Clear Cache**: Restart app weekly
- **Update Regularly**: Install app updates when available
- **Storage Management**: Keep sufficient phone storage available

## ❓ Troubleshooting Common Issues

### "Can't Find My Product"
- Check spelling in search
- Try searching by manufacturer or supplier
- Look in product statistics to see if it exists

### "Customer Not Receiving Messages"
- Verify phone number is correct
- Check if number has changed
- Try calling instead of texting

### "Sales Total Doesn't Match"
- Check for unrecorded sales
- Verify all product prices are current
- Look for duplicate entries

### "App Running Slowly"
- Restart the app
- Clear phone memory
- Update to latest version

## 📞 Getting Help

### In-App Help
- Look for **?** icons throughout the app
- Check tooltips on complex features
- Use the built-in help sections

### Contact Support
- Email: <EMAIL>
- Include your device type and app version
- Describe the specific issue you're experiencing

## 🎓 Advanced Tips

### Keyboard Shortcuts
- Use voice input for product names
- Copy/paste customer information
- Use predictive text for common entries

### Efficiency Hacks
- **Favorite Products**: Mark frequently sold items
- **Customer Groups**: Use notes to categorize customers
- **Supplier Codes**: Add supplier codes to product notes
- **Quick Sales**: Pre-configure common sale combinations

### Business Intelligence
- **Track Seasonal Patterns**: Use reports to identify seasonal trends
- **Supplier Performance**: Monitor delivery reliability
- **Customer Segmentation**: Identify your best customers
- **Pricing Strategy**: Use cost analysis for better pricing

---

## 🎯 Success Metrics

You'll know you're using the app effectively when:
- ✅ You record every sale immediately
- ✅ Customer debts are tracked and collected
- ✅ You know your daily/weekly/monthly profits
- ✅ Inventory levels are monitored
- ✅ Supplier relationships are well-managed
- ✅ Business decisions are data-driven

**Remember**: The app is only as good as the data you put into it. Consistent daily use will give you the best business insights and control over your wholesale operation.

---

*For technical support, contact: <EMAIL>*
*Last updated: January 2025*