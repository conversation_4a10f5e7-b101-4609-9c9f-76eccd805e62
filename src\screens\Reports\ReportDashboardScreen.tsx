import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Sale, Acquisition, Debt, Product, ReportStackParamList } from '@/types';
import { formatCurrency, formatDate } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ReportDashboardNavigationProp = StackNavigationProp<ReportStackParamList, 'ReportDashboard'>;

interface DashboardStats {
  totalSales: number;
  totalRevenue: number;
  totalAcquisitions: number;
  totalAcquisitionCost: number;
  totalDebts: number;
  overdueDebts: number;
  grossProfit: number;
  topSellingProduct: { name: string; quantity: number } | null;
  recentSales: Sale[];
  pendingDeliveries: number;
}

const ReportDashboardScreen: React.FC = () => {
  const navigation = useNavigation<ReportDashboardNavigationProp>();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState<'week' | 'month' | 'year'>('month');

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadDashboardData();
  }, [period]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [sales, acquisitions, debts, products] = await Promise.all([
        db.getAllSales(),
        db.getAllAcquisitions(),
        db.getAllDebts(),
        db.getAllProducts(),
      ]);
      
      const now = new Date();
      const periodStart = getPeriodStart(period, now);
      
      // Filter data by period
      const periodSales = sales.filter(sale => new Date(sale.date) >= periodStart);
      const periodAcquisitions = acquisitions.filter(acq => new Date(acq.date) >= periodStart);
      
      // Calculate stats
      const totalSales = periodSales.length;
      const totalRevenue = periodSales.reduce((sum, sale) => sum + sale.totalAmount, 0);
      const totalAcquisitions = periodAcquisitions.length;
      const totalAcquisitionCost = periodAcquisitions.reduce((sum, acq) => sum + acq.totalAmount, 0);
      const grossProfit = totalRevenue - totalAcquisitionCost;
      
      // Debt stats (all debts, not period-filtered)
      const unpaidDebts = debts.filter(debt => !debt.isPaid);
      const totalDebts = unpaidDebts.reduce((sum, debt) => sum + debt.amount, 0);
      const overdueDebts = unpaidDebts.filter(debt => 
        debt.dueDate && new Date(debt.dueDate) < now
      ).length;
      
      // Top selling product (period-based)
      const productSales: { [key: string]: { name: string; quantity: number } } = {};
      
      periodSales.forEach(sale => {
        sale.items.forEach(item => {
          const product = products.find(p => p.id === item.productId);
          if (product) {
            if (!productSales[item.productId]) {
              productSales[item.productId] = { name: product.name, quantity: 0 };
            }
            productSales[item.productId].quantity += item.quantity;
          }
        });
      });
      
      const topSellingProduct = Object.values(productSales)
        .sort((a, b) => b.quantity - a.quantity)[0] || null;
      
      // Recent sales (last 5)
      const recentSales = sales
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 5);
      
      // Pending deliveries
      const pendingDeliveries = acquisitions.filter(acq => !acq.isDelivered).length;
      
      setStats({
        totalSales,
        totalRevenue,
        totalAcquisitions,
        totalAcquisitionCost,
        totalDebts,
        overdueDebts,
        grossProfit,
        topSellingProduct,
        recentSales,
        pendingDeliveries,
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getPeriodStart = (period: 'week' | 'month' | 'year', now: Date): Date => {
    const start = new Date(now);
    
    switch (period) {
      case 'week':
        start.setDate(now.getDate() - 7);
        break;
      case 'month':
        start.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        start.setFullYear(now.getFullYear() - 1);
        break;
    }
    
    return start;
  };

  const renderStatCard = (
    title: string,
    value: string,
    icon: string,
    color: string,
    subtitle?: string,
    onPress?: () => void
  ) => (
    <Card 
      style={[styles.statCard, { borderLeftColor: color, borderLeftWidth: 4 }]}
      onPress={onPress}
    >
      <View style={styles.statContent}>
        <View style={styles.statInfo}>
          <Text style={styles.statTitle}>{title}</Text>
          <Text style={[styles.statValue, { color }]}>{value}</Text>
          {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
        </View>
        <View style={[styles.statIcon, { backgroundColor: color + '15' }]}>
          <Icon name={icon} size={24} color={color} />
        </View>
      </View>
    </Card>
  );

  const renderQuickAction = (
    title: string,
    icon: string,
    color: string,
    onPress: () => void
  ) => (
    <Card style={styles.actionCard} onPress={onPress}>
      <View style={styles.actionContent}>
        <View style={[styles.actionIcon, { backgroundColor: color + '15' }]}>
          <Icon name={icon} size={28} color={color} />
        </View>
        <Text style={styles.actionTitle}>{title}</Text>
      </View>
    </Card>
  );

  if (loading) {
    return <LoadingSpinner text="Loading dashboard..." />;
  }

  if (!stats) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load dashboard data</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Period Selector */}
        <Card style={styles.periodCard}>
          <Text style={styles.periodTitle}>Analytics Period</Text>
          <View style={styles.periodButtons}>
            {(['week', 'month', 'year'] as const).map((p) => (
              <TouchableOpacity
                key={p}
                style={[styles.periodButton, period === p && styles.activePeriodButton]}
                onPress={() => setPeriod(p)}
              >
                <Text style={[styles.periodButtonText, period === p && styles.activePeriodButtonText]}>
                  {p.charAt(0).toUpperCase() + p.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* Sales & Revenue Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sales & Revenue</Text>
          <View style={styles.statsGrid}>
            {renderStatCard(
              'Total Sales',
              stats.totalSales.toString(),
              'point-of-sale',
              '#4CAF50',
              `Last ${period}`,
              () => navigation.navigate('FinanceReport', { period })
            )}
            {renderStatCard(
              'Revenue',
              formatCurrency(stats.totalRevenue),
              'trending-up',
              '#2196F3',
              `Last ${period}`,
              () => navigation.navigate('FinanceReport', { period })
            )}
            {renderStatCard(
              'Gross Profit',
              formatCurrency(stats.grossProfit),
              'account-balance',
              stats.grossProfit >= 0 ? '#4CAF50' : '#F44336',
              `Revenue - Costs`,
              () => navigation.navigate('FinanceReport', { period })
            )}
          </View>
        </View>

        {/* Acquisition Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Acquisitions & Inventory</Text>
          <View style={styles.statsGrid}>
            {renderStatCard(
              'Acquisitions',
              stats.totalAcquisitions.toString(),
              'inventory',
              '#FF9800',
              `Last ${period}`
            )}
            {renderStatCard(
              'Acquisition Cost',
              formatCurrency(stats.totalAcquisitionCost),
              'money-off',
              '#F44336',
              `Last ${period}`
            )}
            {renderStatCard(
              'Pending Deliveries',
              stats.pendingDeliveries.toString(),
              'local-shipping',
              '#FF5722',
              'Awaiting delivery'
            )}
          </View>
        </View>

        {/* Debt Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Outstanding Debts</Text>
          <View style={styles.statsGrid}>
            {renderStatCard(
              'Total Outstanding',
              formatCurrency(stats.totalDebts),
              'account-balance-wallet',
              '#9C27B0',
              'Unpaid amounts'
            )}
            {renderStatCard(
              'Overdue Debts',
              stats.overdueDebts.toString(),
              'warning',
              '#F44336',
              'Past due date'
            )}
          </View>
        </View>

        {/* Top Product */}
        {stats.topSellingProduct && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Top Performing Product</Text>
            {renderStatCard(
              stats.topSellingProduct.name,
              `${stats.topSellingProduct.quantity} units`,
              'star',
              '#FFD700',
              `Best seller this ${period}`,
              () => navigation.navigate('ProductStatistics')
            )}
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            {renderQuickAction(
              'Finance Report',
              'assessment',
              '#2196F3',
              () => navigation.navigate('FinanceReport', { period })
            )}
            {renderQuickAction(
              'Product Statistics',
              'bar-chart',
              '#FF9800',
              () => navigation.navigate('ProductStatistics')
            )}
          </View>
        </View>

        {/* Recent Sales */}
        {stats.recentSales.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Sales</Text>
            <Card style={styles.recentSalesCard}>
              {stats.recentSales.map((sale, index) => (
                <View key={sale.id} style={[styles.recentSaleItem, index > 0 && styles.recentSaleItemBorder]}>
                  <View style={styles.recentSaleInfo}>
                    <Text style={styles.recentSaleDate}>{formatDate(new Date(sale.date))}</Text>
                    <Text style={styles.recentSaleItems}>{sale.items.length} items</Text>
                  </View>
                  <Text style={styles.recentSaleAmount}>{formatCurrency(sale.totalAmount)}</Text>
                </View>
              ))}
            </Card>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorText: {
    fontSize: 18,
    color: '#999',
  },
  periodCard: {
    margin: 16,
    marginBottom: 8,
  },
  periodTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  periodButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  activePeriodButton: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activePeriodButtonText: {
    color: '#fff',
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  statsGrid: {
    gap: 8,
  },
  statCard: {
    marginBottom: 8,
  },
  statContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statInfo: {
    flex: 1,
  },
  statTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  statSubtitle: {
    fontSize: 12,
    color: '#999',
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: 8,
  },
  actionCard: {
    flex: 1,
    aspectRatio: 1,
  },
  actionContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  recentSalesCard: {
    padding: 0,
  },
  recentSaleItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  recentSaleItemBorder: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  recentSaleInfo: {
    flex: 1,
  },
  recentSaleDate: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  recentSaleItems: {
    fontSize: 12,
    color: '#666',
  },
  recentSaleAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
});

export default ReportDashboardScreen;