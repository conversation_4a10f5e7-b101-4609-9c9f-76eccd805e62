import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import NotificationService from '@/services/notificationService';
import { AppSettings, Product, Provider, Client, Sale, Acquisition, Debt } from '@/types';
import { formatCurrency, formatDate } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface DashboardStats {
  totalProducts: number;
  totalProviders: number;
  totalClients: number;
  todaySales: number;
  todayRevenue: number;
  outstandingDebts: number;
  totalDebtAmount: number;
  pendingDeliveries: number;
}

const DashboardScreen: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalProviders: 0,
    totalClients: 0,
    todaySales: 0,
    todayRevenue: 0,
    outstandingDebts: 0,
    totalDebtAmount: 0,
    pendingDeliveries: 0,
  });
  const [recentSales, setRecentSales] = useState<Sale[]>([]);
  const [recentAcquisitions, setRecentAcquisitions] = useState<Acquisition[]>([]);
  const [urgentDebts, setUrgentDebts] = useState<Debt[]>([]);

  const db = DatabaseService.getInstance();
  const notificationService = NotificationService.getInstance();

  useFocusEffect(
    useCallback(() => {
      loadDashboardData();
    }, [])
  );

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load app settings
      const appSettings = await db.getAppSettings();
      setSettings(appSettings);

      // Load all data in parallel
      const [
        products,
        providers,
        clients,
        sales,
        acquisitions,
        debts,
      ] = await Promise.all([
        db.getAllProducts(),
        db.getAllProviders(),
        db.getAllClients(),
        db.getAllSales(),
        db.getAllAcquisitions(),
        db.getAllDebts(),
      ]);

      // Calculate stats
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const todaySales = sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= today && saleDate < tomorrow;
      });

      const todayRevenue = todaySales.reduce((sum, sale) => sum + sale.totalAmount, 0);
      
      const outstandingDebts = debts.filter(debt => !debt.isPaid);
      const totalDebtAmount = outstandingDebts.reduce((sum, debt) => sum + debt.amount, 0);
      
      const pendingDeliveries = acquisitions.filter(acquisition => !acquisition.isDelivered);

      // Get urgent debts (due within 7 days or overdue)
      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);
      
      const urgent = outstandingDebts.filter(debt => {
        if (!debt.dueDate) return false;
        const dueDate = new Date(debt.dueDate);
        return dueDate <= sevenDaysFromNow;
      }).slice(0, 5);

      setStats({
        totalProducts: products.length,
        totalProviders: providers.length,
        totalClients: clients.length,
        todaySales: todaySales.length,
        todayRevenue,
        outstandingDebts: outstandingDebts.length,
        totalDebtAmount,
        pendingDeliveries: pendingDeliveries.length,
      });

      setRecentSales(sales.slice(0, 5));
      setRecentAcquisitions(acquisitions.slice(0, 5));
      setUrgentDebts(urgent);

      // Check for daily reminder
      await notificationService.checkAndSendDailyReminder();

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleDebtReminder = async () => {
    try {
      await notificationService.sendDebtReminder();
      Alert.alert('Reminder Sent', 'Debt collection reminder has been sent.');
    } catch (error) {
      Alert.alert('Error', 'Failed to send reminder');
    }
  };

  if (loading) {
    return <LoadingSpinner text="Loading dashboard..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.welcomeText}>
            Welcome back, {settings?.userFirstName || 'User'}!
          </Text>
          <Text style={styles.shopName}>{settings?.shopName}</Text>
          <Text style={styles.dateText}>{formatDate(new Date())}</Text>
        </View>

        {/* Quick Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Overview</Text>
          <View style={styles.statsGrid}>
            <Card style={styles.statCard}>
              <Icon name="point-of-sale" size={24} color="#4CAF50" />
              <Text style={styles.statNumber}>{stats.todaySales}</Text>
              <Text style={styles.statLabel}>Sales Today</Text>
            </Card>
            <Card style={styles.statCard}>
              <Icon name="attach-money" size={24} color="#2196F3" />
              <Text style={styles.statNumber}>
                {formatCurrency(stats.todayRevenue, settings?.currency)}
              </Text>
              <Text style={styles.statLabel}>Today's Revenue</Text>
            </Card>
          </View>
        </View>

        {/* Business Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Business Overview</Text>
          <View style={styles.statsGrid}>
            <Card style={styles.statCard}>
              <Icon name="inventory" size={24} color="#FF9800" />
              <Text style={styles.statNumber}>{stats.totalProducts}</Text>
              <Text style={styles.statLabel}>Products</Text>
            </Card>
            <Card style={styles.statCard}>
              <Icon name="business" size={24} color="#9C27B0" />
              <Text style={styles.statNumber}>{stats.totalProviders}</Text>
              <Text style={styles.statLabel}>Providers</Text>
            </Card>
            <Card style={styles.statCard}>
              <Icon name="people" size={24} color="#00BCD4" />
              <Text style={styles.statNumber}>{stats.totalClients}</Text>
              <Text style={styles.statLabel}>Clients</Text>
            </Card>
            <Card style={styles.statCard}>
              <Icon name="local-shipping" size={24} color="#795548" />
              <Text style={styles.statNumber}>{stats.pendingDeliveries}</Text>
              <Text style={styles.statLabel}>Pending Deliveries</Text>
            </Card>
          </View>
        </View>

        {/* Debt Management */}
        {stats.outstandingDebts > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Outstanding Debts</Text>
              <TouchableOpacity
                style={styles.reminderButton}
                onPress={handleDebtReminder}
              >
                <Icon name="notifications" size={20} color="#fff" />
                <Text style={styles.reminderButtonText}>Send Reminder</Text>
              </TouchableOpacity>
            </View>
            <Card style={[styles.debtCard, styles.warningCard]}>
              <View style={styles.debtSummary}>
                <Icon name="warning" size={24} color="#F44336" />
                <View style={styles.debtInfo}>
                  <Text style={styles.debtAmount}>
                    {formatCurrency(stats.totalDebtAmount, settings?.currency)}
                  </Text>
                  <Text style={styles.debtLabel}>
                    {stats.outstandingDebts} outstanding debt{stats.outstandingDebts !== 1 ? 's' : ''}
                  </Text>
                </View>
              </View>
            </Card>

            {urgentDebts.length > 0 && (
              <View>
                <Text style={styles.urgentTitle}>Urgent Debts (Due Soon)</Text>
                {urgentDebts.map((debt) => (
                  <Card key={debt.id} style={styles.urgentDebtCard}>
                    <View style={styles.urgentDebtContent}>
                      <Text style={styles.urgentDebtAmount}>
                        {formatCurrency(debt.amount, settings?.currency)}
                      </Text>
                      <Text style={styles.urgentDebtDate}>
                        Due: {debt.dueDate ? formatDate(new Date(debt.dueDate)) : 'No due date'}
                      </Text>
                    </View>
                  </Card>
                ))}
              </View>
            )}
          </View>
        )}

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          
          {recentSales.length > 0 && (
            <View style={styles.activitySection}>
              <Text style={styles.activityTitle}>Recent Sales</Text>
              {recentSales.map((sale) => (
                <Card key={sale.id} style={styles.activityCard}>
                  <View style={styles.activityContent}>
                    <Icon name="point-of-sale" size={20} color="#4CAF50" />
                    <View style={styles.activityInfo}>
                      <Text style={styles.activityAmount}>
                        {formatCurrency(sale.totalAmount, settings?.currency)}
                      </Text>
                      <Text style={styles.activityDate}>
                        {formatDate(new Date(sale.date))}
                      </Text>
                    </View>
                  </View>
                </Card>
              ))}
            </View>
          )}

          {recentAcquisitions.length > 0 && (
            <View style={styles.activitySection}>
              <Text style={styles.activityTitle}>Recent Acquisitions</Text>
              {recentAcquisitions.map((acquisition) => (
                <Card key={acquisition.id} style={styles.activityCard}>
                  <View style={styles.activityContent}>
                    <Icon name="shopping-cart" size={20} color="#9C27B0" />
                    <View style={styles.activityInfo}>
                      <Text style={styles.activityAmount}>
                        {formatCurrency(acquisition.totalAmount, settings?.currency)}
                      </Text>
                      <Text style={styles.activityDate}>
                        {formatDate(new Date(acquisition.date))}
                      </Text>
                    </View>
                    {!acquisition.isDelivered && (
                      <View style={styles.pendingBadge}>
                        <Text style={styles.pendingText}>Pending</Text>
                      </View>
                    )}
                  </View>
                </Card>
              ))}
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.quickAction}>
              <Icon name="add" size={24} color="#2196F3" />
              <Text style={styles.quickActionText}>Add Sale</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickAction}>
              <Icon name="shopping-cart" size={24} color="#9C27B0" />
              <Text style={styles.quickActionText}>Add Acquisition</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickAction}>
              <Icon name="inventory" size={24} color="#FF9800" />
              <Text style={styles.quickActionText}>Add Product</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickAction}>
              <Icon name="analytics" size={24} color="#607D8B" />
              <Text style={styles.quickActionText}>View Reports</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  shopName: {
    fontSize: 18,
    color: '#666',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    color: '#999',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  debtCard: {
    marginBottom: 12,
  },
  warningCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  debtSummary: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  debtInfo: {
    marginLeft: 12,
    flex: 1,
  },
  debtAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#F44336',
  },
  debtLabel: {
    fontSize: 14,
    color: '#666',
  },
  reminderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F44336',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  reminderButtonText: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '500',
  },
  urgentTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#F44336',
    marginBottom: 8,
  },
  urgentDebtCard: {
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#F44336',
  },
  urgentDebtContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  urgentDebtAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#F44336',
  },
  urgentDebtDate: {
    fontSize: 12,
    color: '#666',
  },
  activitySection: {
    marginBottom: 16,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  activityCard: {
    marginBottom: 8,
    padding: 12,
  },
  activityContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityInfo: {
    marginLeft: 12,
    flex: 1,
  },
  activityAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  activityDate: {
    fontSize: 12,
    color: '#666',
  },
  pendingBadge: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  pendingText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '500',
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAction: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  quickActionText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default DashboardScreen;