import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Alert,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DateTimePicker from '@react-native-community/datetimepicker';
import DatabaseService from '@/services/database';
import { Debt, Client, Sale, DebtorStackParamList } from '@/types';
import { generateId, formatCurrency, formatDate } from '@/utils/helpers';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type DebtorFormProps = StackScreenProps<DebtorStackParamList, 'DebtorForm'>;

interface DebtorForm {
  clientId: string;
  amount: string;
  dueDate?: Date;
  notes?: string;
  saleId?: string;
}

const DebtorFormScreen: React.FC<DebtorFormProps> = () => {
  const navigation = useNavigation();
  const route = useRoute<DebtorFormProps['route']>();
  const { debtId, saleId } = route.params || {};

  const isEditing = !!debtId;

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [form, setForm] = useState<DebtorForm>({
    clientId: '',
    amount: '',
    saleId: saleId,
  });
  const [errors, setErrors] = useState<Partial<DebtorForm>>({});
  
  // Modal states
  const [showClientModal, setShowClientModal] = useState(false);
  const [showSaleModal, setShowSaleModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  
  // Data states
  const [clients, setClients] = useState<Client[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (isEditing) {
      loadDebtData();
    }
  }, [isEditing]);

  useEffect(() => {
    if (saleId) {
      loadSaleData();
    }
  }, [saleId]);

  const loadInitialData = async () => {
    try {
      const [clientsData, salesData] = await Promise.all([
        db.getAllClients(),
        db.getAllSales(),
      ]);
      setClients(clientsData);
      setSales(salesData.filter(sale => !sale.debt)); // Only unpaid sales
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load data');
    }
  };

  const loadSaleData = async () => {
    try {
      const saleData = await db.getSaleById(saleId!);
      if (saleData) {
        setSelectedSale(saleData);
        setForm(prev => ({
          ...prev,
          clientId: saleData.clientId || '',
          amount: saleData.totalAmount.toString(),
          saleId: saleData.id,
        }));
        
        if (saleData.clientId) {
          const clientData = await db.getClientById(saleData.clientId);
          if (clientData) {
            setSelectedClient(clientData);
          }
        }
      }
    } catch (error) {
      console.error('Error loading sale data:', error);
    }
  };

  const loadDebtData = async () => {
    try {
      setLoading(true);
      const debtData = await db.getDebtById(debtId!);
      
      if (debtData) {
        setForm({
          clientId: debtData.clientId || '',
          amount: debtData.amount.toString(),
          dueDate: debtData.dueDate ? new Date(debtData.dueDate) : undefined,
          notes: debtData.notes,
          saleId: debtData.saleId,
        });

        // Load related data
        if (debtData.clientId) {
          const clientData = await db.getClientById(debtData.clientId);
          if (clientData) {
            setSelectedClient(clientData);
          }
        }

        if (debtData.saleId) {
          const saleData = await db.getSaleById(debtData.saleId);
          if (saleData) {
            setSelectedSale(saleData);
          }
        }
      }
    } catch (error) {
      console.error('Error loading debt data:', error);
      Alert.alert('Error', 'Failed to load debt data');
    } finally {
      setLoading(false);
    }
  };

  const updateForm = <K extends keyof DebtorForm>(field: K, value: DebtorForm[K]) => {
    setForm(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<DebtorForm> = {};

    if (!form.clientId.trim()) {
      newErrors.clientId = 'Client is required';
    }

    if (!form.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(form.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Please enter a valid amount';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);
      const amount = parseFloat(form.amount);

      const debtData: Omit<Debt, 'id' | 'createdAt' | 'updatedAt'> = {
        clientId: form.clientId,
        amount,
        dateOfDebt: new Date(),
        dueDate: form.dueDate,
        notes: form.notes,
        isPaid: false,
        saleId: form.saleId,
      };

      if (isEditing) {
        await db.updateDebt(debtId!, {
          ...debtData,
          id: debtId!,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        Alert.alert('Success', 'Debt updated successfully');
      } else {
        const newDebt: Debt = {
          ...debtData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        await db.createDebt(newDebt);
        
        // Update sale with debt info if saleId exists
        if (form.saleId) {
          const saleData = await db.getSaleById(form.saleId);
          if (saleData) {
            await db.updateSale(form.saleId, {
              ...saleData,
              debt: newDebt.id,
            });
          }
        }
        
        Alert.alert('Success', 'Debt created successfully');
      }

      navigation.goBack();
    } catch (error) {
      console.error('Error saving debt:', error);
      Alert.alert('Error', 'Failed to save debt');
    } finally {
      setSaving(false);
    }
  };

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    updateForm('clientId', client.id);
    setShowClientModal(false);
  };

  const handleSaleSelect = (sale: Sale) => {
    setSelectedSale(sale);
    updateForm('saleId', sale.id);
    updateForm('amount', sale.totalAmount.toString());
    if (sale.clientId) {
      updateForm('clientId', sale.clientId);
      const client = clients.find(c => c.id === sale.clientId);
      if (client) {
        setSelectedClient(client);
      }
    }
    setShowSaleModal(false);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      updateForm('dueDate', selectedDate);
    }
  };

  const renderClientModal = () => (
    <Modal visible={showClientModal} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Select Client</Text>
          <TouchableOpacity onPress={() => setShowClientModal(false)}>
            <Icon name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>
        
        <FlatList
          data={clients}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.clientItem}
              onPress={() => handleClientSelect(item)}
            >
              <View>
                <Text style={styles.clientName}>
                  {item.firstName} {item.lastName || ''}
                </Text>
                <Text style={styles.clientPhone}>{item.phoneNumber}</Text>
              </View>
            </TouchableOpacity>
          )}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      </SafeAreaView>
    </Modal>
  );

  const renderSaleModal = () => (
    <Modal visible={showSaleModal} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Select Sale</Text>
          <TouchableOpacity onPress={() => setShowSaleModal(false)}>
            <Icon name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>
        
        <FlatList
          data={sales}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => {
            const client = clients.find(c => c.id === item.clientId);
            return (
              <TouchableOpacity
                style={styles.saleItem}
                onPress={() => handleSaleSelect(item)}
              >
                <View style={styles.saleInfo}>
                  <Text style={styles.saleDate}>{formatDate(new Date(item.date))}</Text>
                  <Text style={styles.saleAmount}>{formatCurrency(item.totalAmount)}</Text>
                  {client && (
                    <Text style={styles.saleClient}>
                      {client.firstName} {client.lastName || ''}
                    </Text>
                  )}
                </View>
                <Icon name="chevron-right" size={20} color="#666" />
              </TouchableOpacity>
            );
          }}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      </SafeAreaView>
    </Modal>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.formCard}>
          <Text style={styles.sectionTitle}>Debt Information</Text>
          
          {/* Client Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Client *</Text>
            <TouchableOpacity
              style={[styles.selector, errors.clientId && styles.inputError]}
              onPress={() => setShowClientModal(true)}
            >
              <Text style={[styles.selectorText, !selectedClient && styles.placeholder]}>
                {selectedClient
                  ? `${selectedClient.firstName} ${selectedClient.lastName || ''}`
                  : 'Select Client'
                }
              </Text>
              <Icon name="chevron-right" size={20} color="#666" />
            </TouchableOpacity>
            {errors.clientId && <Text style={styles.errorText}>{errors.clientId}</Text>}
          </View>

          {/* Sale Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Related Sale (Optional)</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowSaleModal(true)}
            >
              <Text style={[styles.selectorText, !selectedSale && styles.placeholder]}>
                {selectedSale
                  ? `${formatDate(new Date(selectedSale.date))} - ${formatCurrency(selectedSale.totalAmount)}`
                  : 'Select Sale'
                }
              </Text>
              <Icon name="chevron-right" size={20} color="#666" />
            </TouchableOpacity>
            {selectedSale && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => {
                  setSelectedSale(null);
                  updateForm('saleId', undefined);
                }}
              >
                <Text style={styles.clearButtonText}>Clear Selection</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Amount */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Amount *</Text>
            <Input
              placeholder="0.00"
              value={form.amount}
              onChangeText={(value) => updateForm('amount', value)}
              keyboardType="numeric"
              error={errors.amount}
            />
          </View>

          {/* Due Date */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Due Date (Optional)</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={[styles.selectorText, !form.dueDate && styles.placeholder]}>
                {form.dueDate ? formatDate(form.dueDate) : 'Select Due Date'}
              </Text>
              <Icon name="event" size={20} color="#666" />
            </TouchableOpacity>
            {form.dueDate && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => updateForm('dueDate', undefined)}
              >
                <Text style={styles.clearButtonText}>Clear Date</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Notes */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes (Optional)</Text>
            <Input
              placeholder="Additional notes about this debt..."
              value={form.notes || ''}
              onChangeText={(value) => updateForm('notes', value)}
              multiline
              numberOfLines={3}
            />
          </View>
        </Card>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title={isEditing ? 'Update Debt' : 'Create Debt'}
          onPress={handleSave}
          loading={saving}
          style={styles.saveButton}
        />
      </View>

      {showDatePicker && (
        <DateTimePicker
          value={form.dueDate || new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={new Date()}
        />
      )}

      {renderClientModal()}
      {renderSaleModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    minHeight: 48,
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  placeholder: {
    color: '#999',
  },
  inputError: {
    borderColor: '#ff4444',
  },
  errorText: {
    color: '#ff4444',
    fontSize: 12,
    marginTop: 4,
  },
  clearButton: {
    marginTop: 8,
  },
  clearButtonText: {
    color: '#007AFF',
    fontSize: 14,
  },
  footer: {
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  saveButton: {
    backgroundColor: '#007AFF',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  clientItem: {
    padding: 16,
    backgroundColor: 'white',
  },
  clientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  clientPhone: {
    fontSize: 14,
    color: '#666',
  },
  saleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
  },
  saleInfo: {
    flex: 1,
  },
  saleDate: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  saleAmount: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: '600',
    marginBottom: 2,
  },
  saleClient: {
    fontSize: 12,
    color: '#666',
  },
  separator: {
    height: 1,
    backgroundColor: '#e0e0e0',
  },
});

export default DebtorFormScreen;