import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Client, ClientStackParamList } from '@/types';
import { makePhoneCall, sendSMS, formatDate } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ClientListNavigationProp = StackNavigationProp<ClientStackParamList, 'ClientList'>;

const ClientListScreen: React.FC = () => {
  const navigation = useNavigation<ClientListNavigationProp>();
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useFocusEffect(
    useCallback(() => {
      loadClients();
    }, [])
  );

  const loadClients = async () => {
    try {
      setLoading(true);
      const allClients = await db.getAllClients();
      setClients(allClients);
      setFilteredClients(allClients);
    } catch (error) {
      console.error('Error loading clients:', error);
      Alert.alert('Error', 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim() === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(client =>
        client.firstName.toLowerCase().includes(query.toLowerCase()) ||
        client.lastName?.toLowerCase().includes(query.toLowerCase()) ||
        client.phoneNumber.includes(query)
      );
      setFilteredClients(filtered);
    }
  };

  const handleDeleteClient = (client: Client) => {
    Alert.alert(
      'Delete Client',
      `Are you sure you want to delete "${client.firstName} ${client.lastName || ''}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteClient(client.id);
              await loadClients();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete client');
            }
          },
        },
      ]
    );
  };

  const handleContactClient = (client: Client, method: 'call' | 'sms') => {
    if (method === 'call') {
      makePhoneCall(client.phoneNumber);
    } else {
      sendSMS(client.phoneNumber);
    }
  };

  const renderClient = ({ item }: { item: Client }) => (
    <Card
      style={styles.clientCard}
      onPress={() => navigation.navigate('ClientDetails', { clientId: item.id })}
    >
      <View style={styles.clientHeader}>
        <View style={styles.clientInfo}>
          <Text style={styles.clientName}>
            {item.firstName} {item.lastName || ''}
          </Text>
          <Text style={styles.phoneNumber}>{item.phoneNumber}</Text>
          {item.note && (
            <Text style={styles.clientNote} numberOfLines={2}>
              {item.note}
            </Text>
          )}
          <Text style={styles.addedDate}>
            Added {formatDate(new Date(item.createdAt))}
          </Text>
        </View>
        <View style={styles.clientActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleContactClient(item, 'call')}
          >
            <Icon name="phone" size={20} color="#4CAF50" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleContactClient(item, 'sms')}
          >
            <Icon name="sms" size={20} color="#2196F3" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('ClientForm', { clientId: item.id })}
          >
            <Icon name="edit" size={20} color="#FF9800" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteClient(item)}
          >
            <Icon name="delete" size={20} color="#F44336" />
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="people" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>No Clients Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? 'No clients match your search criteria.'
          : 'Add your first client to get started.'
        }
      </Text>
    </View>
  );

  if (loading) {
    return <LoadingSpinner text="Loading clients..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Input
          placeholder="Search clients..."
          value={searchQuery}
          onChangeText={handleSearch}
          containerStyle={styles.searchContainer}
          inputStyle={styles.searchInput}
        />
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('ClientForm', {})}
        >
          <Icon name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.resultInfo}>
        <Text style={styles.resultCount}>
          {filteredClients.length} client{filteredClients.length !== 1 ? 's' : ''}
          {searchQuery && ` found for "${searchQuery}"`}
        </Text>
      </View>

      <FlatList
        data={filteredClients}
        renderItem={renderClient}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  searchContainer: {
    flex: 1,
    marginBottom: 0,
    marginRight: 12,
  },
  searchInput: {
    borderRadius: 25,
    paddingHorizontal: 16,
  },
  addButton: {
    backgroundColor: '#FF9800',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  resultInfo: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  resultCount: {
    fontSize: 14,
    color: '#666',
  },
  listContent: {
    padding: 8,
    flexGrow: 1,
  },
  clientCard: {
    marginBottom: 8,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  clientInfo: {
    flex: 1,
    marginRight: 12,
  },
  clientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  phoneNumber: {
    fontSize: 14,
    color: '#2196F3',
    marginBottom: 4,
  },
  clientNote: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    lineHeight: 18,
  },
  addedDate: {
    fontSize: 12,
    color: '#999',
  },
  clientActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default ClientListScreen;