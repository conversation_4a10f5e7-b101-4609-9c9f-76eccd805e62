import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Debt, Client, Sale, DebtorStackParamList } from '@/types';
import { formatDate, formatCurrency, makePhoneCall, sendSMS, getDaysUntil } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type DebtorDetailsProps = StackScreenProps<DebtorStackParamList, 'DebtorDetails'>;
type DebtorDetailsNavigationProp = StackNavigationProp<DebtorStackParamList, 'DebtorDetails'>;

const DebtorDetailsScreen: React.FC<DebtorDetailsProps> = () => {
  const navigation = useNavigation<DebtorDetailsNavigationProp>();
  const route = useRoute<DebtorDetailsProps['route']>();
  const { debtId } = route.params;

  const [debt, setDebt] = useState<Debt | null>(null);
  const [debtor, setDebtor] = useState<Client | null>(null);
  const [relatedSale, setRelatedSale] = useState<Sale | null>(null);
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadDebtDetails();
  }, [debtId]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadDebtDetails();
    });
    return unsubscribe;
  }, [navigation]);

  const loadDebtDetails = async () => {
    try {
      setLoading(true);
      
      const debtData = await db.getDebtById(debtId);

      if (!debtData) {
        Alert.alert('Error', 'Debt not found', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
        return;
      }

      setDebt(debtData);
      
      // Load debtor (client) information
      const debtorData = await db.getClientById(debtData.debtorId);
      setDebtor(debtorData);
      
      // Load related sale if exists
      if (debtData.saleId) {
        const saleData = await db.getSaleById(debtData.saleId);
        setRelatedSale(saleData);
      }
    } catch (error) {
      console.error('Error loading debt details:', error);
      Alert.alert('Error', 'Failed to load debt details');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('DebtorForm', { debtId });
  };

  const handleDelete = () => {
    if (!debt) return;
    
    Alert.alert(
      'Delete Debt',
      `Are you sure you want to delete this debt of ${formatCurrency(debt.amount)}?\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteDebt(debtId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete debt');
            }
          },
        },
      ]
    );
  };

  const handleMarkAsPaid = async () => {
    if (!debt) return;
    
    Alert.alert(
      'Mark as Paid',
      `Mark debt of ${formatCurrency(debt.amount)} as paid?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Mark Paid',
          onPress: async () => {
            try {
              const updatedDebt = { ...debt, isPaid: true, updatedAt: new Date() };
              await db.updateDebt(updatedDebt);
              await loadDebtDetails();
            } catch (error) {
              Alert.alert('Error', 'Failed to update debt');
            }
          },
        },
      ]
    );
  };

  const handleContactDebtor = (method: 'call' | 'sms') => {
    if (!debtor) return;
    
    if (method === 'call') {
      makePhoneCall(debtor.phoneNumber);
    } else {
      const message = `Hi ${debtor.firstName}, this is a reminder about your outstanding debt of ${formatCurrency(debt?.amount || 0)}. Please contact us to arrange payment. Thank you.`;
      sendSMS(debtor.phoneNumber, message);
    }
  };

  const isOverdue = (): boolean => {
    if (!debt?.dueDate || debt.isPaid) return false;
    return new Date(debt.dueDate) < new Date();
  };

  const getDaysOverdue = (): number => {
    if (!debt?.dueDate || debt.isPaid) return 0;
    const now = new Date();
    const dueDate = new Date(debt.dueDate);
    if (dueDate >= now) return 0;
    return Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
  };

  const getDaysUntilDue = (): number => {
    if (!debt?.dueDate || debt.isPaid) return 0;
    return getDaysUntil(new Date(debt.dueDate));
  };

  if (loading) {
    return <LoadingSpinner text="Loading debt details..." />;
  }

  if (!debt) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Debt not found</Text>
      </View>
    );
  }

  const overdue = isOverdue();
  const daysOverdue = getDaysOverdue();
  const daysUntilDue = getDaysUntilDue();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Debt Header */}
        <Card style={[styles.headerCard, overdue && styles.overdueCard]}>
          <View style={styles.debtHeader}>
            <View style={styles.debtInfo}>
              <Text style={styles.debtId}>Debt #{debt.id.slice(-8).toUpperCase()}</Text>
              <Text style={styles.debtDate}>{formatDate(new Date(debt.dateOfDebt))}</Text>
              {debt.dueDate && (
                <Text style={[styles.dueDate, overdue && styles.overdueDueDate]}>
                  Due: {formatDate(new Date(debt.dueDate))}
                  {overdue && ` (${daysOverdue} days overdue)`}
                  {!overdue && daysUntilDue > 0 && ` (${daysUntilDue} days left)`}
                </Text>
              )}
            </View>
            <View style={styles.amountContainer}>
              <Text style={[styles.debtAmount, debt.isPaid && styles.paidAmount]}>
                {formatCurrency(debt.amount)}
              </Text>
              {debt.isPaid ? (
                <View style={styles.paidBadge}>
                  <Icon name="check-circle" size={16} color="#4CAF50" />
                  <Text style={styles.paidText}>Paid</Text>
                </View>
              ) : overdue ? (
                <View style={styles.overdueBadge}>
                  <Icon name="warning" size={16} color="#F44336" />
                  <Text style={styles.overdueText}>Overdue</Text>
                </View>
              ) : (
                <View style={styles.pendingBadge}>
                  <Icon name="schedule" size={16} color="#FF9800" />
                  <Text style={styles.pendingText}>Pending</Text>
                </View>
              )}
            </View>
          </View>
        </Card>

        {/* Debtor Information */}
        <Card style={styles.debtorCard}>
          <Text style={styles.sectionTitle}>Debtor Information</Text>
          {debtor ? (
            <View>
              <View style={styles.debtorInfo}>
                <Text style={styles.debtorName}>
                  {debtor.firstName} {debtor.lastName || ''}
                </Text>
                <Text style={styles.debtorPhone}>{debtor.phoneNumber}</Text>
                {debtor.note && (
                  <Text style={styles.debtorNote}>{debtor.note}</Text>
                )}
              </View>
              
              {!debt.isPaid && (
                <View style={styles.contactActions}>
                  <TouchableOpacity
                    style={[styles.contactButton, styles.callButton]}
                    onPress={() => handleContactDebtor('call')}
                  >
                    <Icon name="phone" size={18} color="#fff" />
                    <Text style={styles.contactButtonText}>Call</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.contactButton, styles.smsButton]}
                    onPress={() => handleContactDebtor('sms')}
                  >
                    <Icon name="sms" size={18} color="#fff" />
                    <Text style={styles.contactButtonText}>Send Reminder</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          ) : (
            <View style={styles.unknownDebtor}>
              <Icon name="person" size={24} color="#999" />
              <Text style={styles.unknownDebtorText}>Unknown Debtor</Text>
              <Text style={styles.unknownDebtorSubtext}>Debtor information not found</Text>
            </View>
          )}
        </Card>

        {/* Related Sale */}
        {relatedSale && (
          <Card style={styles.saleCard}>
            <Text style={styles.sectionTitle}>Related Sale</Text>
            <View style={styles.saleInfo}>
              <View style={styles.saleDetails}>
                <Text style={styles.saleDate}>
                  Sale Date: {formatDate(new Date(relatedSale.date))}
                </Text>
                <Text style={styles.saleAmount}>
                  Sale Amount: {formatCurrency(relatedSale.totalAmount)}
                </Text>
                <Text style={styles.saleItems}>
                  Items: {relatedSale.items.length} product{relatedSale.items.length !== 1 ? 's' : ''}
                </Text>
                <Text style={styles.receiptStatus}>
                  Receipt: {relatedSale.receiptStatus === 'issued' ? 'Issued' : 'Not Issued'}
                </Text>
              </View>
            </View>
          </Card>
        )}

        {/* Debt Notes */}
        {debt.notes && (
          <Card style={styles.notesCard}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <Text style={styles.notesText}>{debt.notes}</Text>
          </Card>
        )}

        {/* Reminder Settings */}
        <Card style={styles.reminderCard}>
          <Text style={styles.sectionTitle}>Reminder Settings</Text>
          <View style={styles.reminderInfo}>
            <View style={styles.reminderRow}>
              <Text style={styles.reminderLabel}>Daily Reminders:</Text>
              <Text style={styles.reminderValue}>
                {debt.reminderSettings.dailyReminderEnabled ? 'Enabled' : 'Disabled'}
              </Text>
            </View>
            {debt.reminderSettings.customReminders.length > 0 && (
              <View style={styles.reminderRow}>
                <Text style={styles.reminderLabel}>Custom Reminders:</Text>
                <Text style={styles.reminderValue}>
                  {debt.reminderSettings.customReminders.length} set
                </Text>
              </View>
            )}
          </View>
        </Card>

        {/* Payment Action */}
        {!debt.isPaid && (
          <Card style={styles.paymentCard}>
            <Text style={styles.sectionTitle}>Payment Action</Text>
            <TouchableOpacity
              style={styles.markPaidButton}
              onPress={handleMarkAsPaid}
            >
              <Icon name="payment" size={24} color="#4CAF50" />
              <Text style={styles.markPaidText}>Mark as Paid</Text>
            </TouchableOpacity>
          </Card>
        )}
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          title="Edit Debt"
          onPress={handleEdit}
          style={styles.editButton}
          textStyle={styles.editButtonText}
        />
        <Button
          title="Delete"
          onPress={handleDelete}
          style={styles.deleteButton}
          textStyle={styles.deleteButtonText}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorText: {
    fontSize: 18,
    color: '#999',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  debtHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  debtInfo: {
    flex: 1,
  },
  debtId: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  debtDate: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  dueDate: {
    fontSize: 14,
    color: '#FF9800',
    fontWeight: '500',
  },
  overdueDueDate: {
    color: '#F44336',
    fontWeight: 'bold',
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  debtAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#F44336',
    marginBottom: 8,
  },
  paidAmount: {
    color: '#999',
    textDecorationLine: 'line-through',
  },
  paidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  paidText: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '500',
  },
  overdueBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  overdueText: {
    fontSize: 14,
    color: '#F44336',
    fontWeight: '500',
  },
  pendingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  pendingText: {
    fontSize: 14,
    color: '#FF9800',
    fontWeight: '500',
  },
  debtorCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  debtorInfo: {
    marginBottom: 16,
  },
  debtorName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  debtorPhone: {
    fontSize: 14,
    color: '#2196F3',
    marginBottom: 4,
  },
  debtorNote: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  contactActions: {
    flexDirection: 'row',
    gap: 12,
  },
  contactButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 6,
    gap: 6,
  },
  callButton: {
    backgroundColor: '#4CAF50',
  },
  smsButton: {
    backgroundColor: '#2196F3',
  },
  contactButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  unknownDebtor: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  unknownDebtorText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#999',
    marginTop: 8,
    marginBottom: 4,
  },
  unknownDebtorSubtext: {
    fontSize: 12,
    color: '#ccc',
  },
  saleCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  saleInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  saleDetails: {
    flex: 1,
  },
  saleDate: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  saleAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 4,
  },
  saleItems: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  receiptStatus: {
    fontSize: 14,
    color: '#666',
  },
  notesCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  reminderCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  reminderInfo: {
    gap: 8,
  },
  reminderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reminderLabel: {
    fontSize: 14,
    color: '#666',
  },
  reminderValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  paymentCard: {
    marginHorizontal: 16,
    marginBottom: 80,
  },
  markPaidButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#4CAF50' + '15',
    borderRadius: 8,
    gap: 12,
  },
  markPaidText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  editButton: {
    flex: 1,
    backgroundColor: '#FF9800',
  },
  editButtonText: {
    color: '#fff',
  },
  deleteButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 24,
  },
  deleteButtonText: {
    color: '#fff',
  },
});

export default DebtorDetailsScreen;