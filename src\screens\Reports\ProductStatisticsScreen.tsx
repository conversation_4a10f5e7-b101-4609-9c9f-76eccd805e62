import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  FlatList,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Sale, Acquisition, Product } from '@/types';
import { formatCurrency, formatDate } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface ProductStats {
  id: string;
  name: string;
  unitOfMeasure: string;
  totalSold: number;
  totalRevenue: number;
  totalAcquired: number;
  totalCost: number;
  currentStock: number;
  averageSellingPrice: number;
  averageCostPrice: number;
  profit: number;
  profitMargin: number;
  lastSaleDate?: Date;
  lastAcquisitionDate?: Date;
  salesTrend: 'up' | 'down' | 'stable';
  category: 'bestseller' | 'slow_moving' | 'new' | 'regular';
}

type SortBy = 'revenue' | 'quantity' | 'profit' | 'margin' | 'stock';
type FilterBy = 'all' | 'bestseller' | 'slow_moving' | 'low_stock' | 'profitable';

const ProductStatisticsScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [loading, setLoading] = useState(false);
  const [productStats, setProductStats] = useState<ProductStats[]>([]);
  const [filteredStats, setFilteredStats] = useState<ProductStats[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortBy>('revenue');
  const [filterBy, setFilterBy] = useState<FilterBy>('all');
  const [period, setPeriod] = useState<'month' | 'quarter' | 'year'>('month');

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadProductStatistics();
  }, [period]);

  useEffect(() => {
    applyFiltersAndSort();
  }, [productStats, searchQuery, sortBy, filterBy]);

  const getPeriodStart = (): Date => {
    const now = new Date();
    const start = new Date();
    
    switch (period) {
      case 'month':
        start.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        start.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        start.setFullYear(now.getFullYear() - 1);
        break;
    }
    
    return start;
  };

  const loadProductStatistics = async () => {
    try {
      setLoading(true);
      
      const [products, sales, acquisitions] = await Promise.all([
        db.getAllProducts(),
        db.getAllSales(),
        db.getAllAcquisitions(),
      ]);

      const periodStart = getPeriodStart();
      const periodSales = sales.filter(sale => new Date(sale.date) >= periodStart);
      const periodAcquisitions = acquisitions.filter(acq => new Date(acq.date) >= periodStart);

      const stats: ProductStats[] = products.map(product => {
        // Calculate sales data
        const productSales = periodSales.reduce((acc, sale) => {
          const saleItems = sale.items.filter(item => item.productId === product.id);
          return acc + saleItems.reduce((sum, item) => sum + item.quantity, 0);
        }, 0);

        const productRevenue = periodSales.reduce((acc, sale) => {
          const saleItems = sale.items.filter(item => item.productId === product.id);
          return acc + saleItems.reduce((sum, item) => sum + item.totalPrice, 0);
        }, 0);

        // Calculate acquisition data
        const productAcquisitions = periodAcquisitions.reduce((acc, acq) => {
          const acqItems = acq.items.filter(item => item.productId === product.id);
          return acc + acqItems.reduce((sum, item) => sum + item.quantity, 0);
        }, 0);

        const productCost = periodAcquisitions.reduce((acc, acq) => {
          const acqItems = acq.items.filter(item => item.productId === product.id);
          return acc + acqItems.reduce((sum, item) => sum + item.totalPrice, 0);
        }, 0);

        // Calculate derived metrics
        const currentStock = productAcquisitions - productSales;
        const averageSellingPrice = productSales > 0 ? productRevenue / productSales : 0;
        const averageCostPrice = productAcquisitions > 0 ? productCost / productAcquisitions : 0;
        const profit = productRevenue - productCost;
        const profitMargin = productRevenue > 0 ? (profit / productRevenue) * 100 : 0;

        // Find last sale and acquisition dates
        const lastSale = periodSales
          .filter(sale => sale.items.some(item => item.productId === product.id))
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
        
        const lastAcquisition = periodAcquisitions
          .filter(acq => acq.items.some(item => item.productId === product.id))
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

        // Determine sales trend (simplified)
        const salesTrend: 'up' | 'down' | 'stable' = productSales > productAcquisitions * 0.7 ? 'up' : 
                                                    productSales < productAcquisitions * 0.3 ? 'down' : 'stable';

        // Categorize product
        let category: 'bestseller' | 'slow_moving' | 'new' | 'regular' = 'regular';
        if (productSales === 0 && productAcquisitions > 0) {
          category = 'slow_moving';
        } else if (productRevenue > 1000) { // Bestseller threshold
          category = 'bestseller';
        } else if (!lastAcquisition || new Date().getTime() - new Date(lastAcquisition.date).getTime() < 30 * 24 * 60 * 60 * 1000) {
          category = 'new';
        }

        return {
          id: product.id,
          name: product.name,
          unitOfMeasure: product.unitOfMeasure,
          totalSold: productSales,
          totalRevenue: productRevenue,
          totalAcquired: productAcquisitions,
          totalCost: productCost,
          currentStock,
          averageSellingPrice,
          averageCostPrice,
          profit,
          profitMargin,
          lastSaleDate: lastSale ? new Date(lastSale.date) : undefined,
          lastAcquisitionDate: lastAcquisition ? new Date(lastAcquisition.date) : undefined,
          salesTrend,
          category,
        };
      });

      setProductStats(stats);
      
    } catch (error) {
      console.error('Error loading product statistics:', error);
      Alert.alert('Error', 'Failed to load product statistics');
    } finally {
      setLoading(false);
    }
  };

  const applyFiltersAndSort = () => {
    let filtered = [...productStats];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(stat =>
        stat.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    switch (filterBy) {
      case 'bestseller':
        filtered = filtered.filter(stat => stat.category === 'bestseller');
        break;
      case 'slow_moving':
        filtered = filtered.filter(stat => stat.category === 'slow_moving');
        break;
      case 'low_stock':
        filtered = filtered.filter(stat => stat.currentStock < 10);
        break;
      case 'profitable':
        filtered = filtered.filter(stat => stat.profit > 0);
        break;
    }

    // Apply sorting
    switch (sortBy) {
      case 'revenue':
        filtered.sort((a, b) => b.totalRevenue - a.totalRevenue);
        break;
      case 'quantity':
        filtered.sort((a, b) => b.totalSold - a.totalSold);
        break;
      case 'profit':
        filtered.sort((a, b) => b.profit - a.profit);
        break;
      case 'margin':
        filtered.sort((a, b) => b.profitMargin - a.profitMargin);
        break;
      case 'stock':
        filtered.sort((a, b) => b.currentStock - a.currentStock);
        break;
    }

    setFilteredStats(filtered);
  };

  const renderPeriodSelector = () => (
    <Card style={styles.periodCard}>
      <Text style={styles.sectionTitle}>Analysis Period</Text>
      <View style={styles.periodButtons}>
        {(['month', 'quarter', 'year'] as const).map((p) => (
          <TouchableOpacity
            key={p}
            style={[
              styles.periodButton,
              period === p && styles.activePeriodButton,
            ]}
            onPress={() => setPeriod(p)}
          >
            <Text
              style={[
                styles.periodButtonText,
                period === p && styles.activePeriodButtonText,
              ]}
            >
              {p.charAt(0).toUpperCase() + p.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );

  const renderFiltersAndSort = () => (
    <Card style={styles.filtersCard}>
      <Text style={styles.sectionTitle}>Filters & Sort</Text>
      
      {/* Search */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search products..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Filter buttons */}
      <View style={styles.filterRow}>
        <Text style={styles.filterLabel}>Filter:</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.filterButtons}>
            {([
              { key: 'all', label: 'All' },
              { key: 'bestseller', label: 'Best Sellers' },
              { key: 'slow_moving', label: 'Slow Moving' },
              { key: 'low_stock', label: 'Low Stock' },
              { key: 'profitable', label: 'Profitable' },
            ] as const).map((filter) => (
              <TouchableOpacity
                key={filter.key}
                style={[
                  styles.filterButton,
                  filterBy === filter.key && styles.activeFilterButton,
                ]}
                onPress={() => setFilterBy(filter.key)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    filterBy === filter.key && styles.activeFilterButtonText,
                  ]}
                >
                  {filter.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Sort buttons */}
      <View style={styles.filterRow}>
        <Text style={styles.filterLabel}>Sort by:</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.filterButtons}>
            {([
              { key: 'revenue', label: 'Revenue' },
              { key: 'quantity', label: 'Quantity' },
              { key: 'profit', label: 'Profit' },
              { key: 'margin', label: 'Margin' },
              { key: 'stock', label: 'Stock' },
            ] as const).map((sort) => (
              <TouchableOpacity
                key={sort.key}
                style={[
                  styles.filterButton,
                  sortBy === sort.key && styles.activeFilterButton,
                ]}
                onPress={() => setSortBy(sort.key)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    sortBy === sort.key && styles.activeFilterButtonText,
                  ]}
                >
                  {sort.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    </Card>
  );

  const renderOverviewStats = () => {
    const totalProducts = productStats.length;
    const totalRevenue = productStats.reduce((sum, stat) => sum + stat.totalRevenue, 0);
    const totalProfit = productStats.reduce((sum, stat) => sum + stat.profit, 0);
    const bestSellers = productStats.filter(stat => stat.category === 'bestseller').length;
    const slowMoving = productStats.filter(stat => stat.category === 'slow_moving').length;

    return (
      <Card style={styles.overviewCard}>
        <Text style={styles.sectionTitle}>Overview</Text>
        
        <View style={styles.overviewGrid}>
          <View style={styles.overviewItem}>
            <Icon name="inventory" size={24} color="#007AFF" />
            <Text style={styles.overviewValue}>{totalProducts}</Text>
            <Text style={styles.overviewLabel}>Total Products</Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Icon name="trending-up" size={24} color="#28a745" />
            <Text style={styles.overviewValue}>{formatCurrency(totalRevenue)}</Text>
            <Text style={styles.overviewLabel}>Total Revenue</Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Icon name="account-balance-wallet" size={24} color="#ffc107" />
            <Text style={styles.overviewValue}>{formatCurrency(totalProfit)}</Text>
            <Text style={styles.overviewLabel}>Total Profit</Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Icon name="star" size={24} color="#ff6b6b" />
            <Text style={styles.overviewValue}>{bestSellers}</Text>
            <Text style={styles.overviewLabel}>Best Sellers</Text>
          </View>
        </View>

        {slowMoving > 0 && (
          <View style={styles.alertContainer}>
            <Icon name="warning" size={20} color="#ffc107" />
            <Text style={styles.alertText}>
              {slowMoving} products have slow movement
            </Text>
          </View>
        )}
      </Card>
    );
  };

  const renderProductItem = ({ item }: { item: ProductStats }) => {
    const getCategoryColor = (category: string) => {
      switch (category) {
        case 'bestseller': return '#28a745';
        case 'slow_moving': return '#dc3545';
        case 'new': return '#007AFF';
        default: return '#6c757d';
      }
    };

    const getTrendIcon = (trend: string) => {
      switch (trend) {
        case 'up': return 'trending-up';
        case 'down': return 'trending-down';
        default: return 'trending-flat';
      }
    };

    const getTrendColor = (trend: string) => {
      switch (trend) {
        case 'up': return '#28a745';
        case 'down': return '#dc3545';
        default: return '#6c757d';
      }
    };

    return (
      <Card style={styles.productCard}>
        <View style={styles.productHeader}>
          <View style={styles.productTitleContainer}>
            <Text style={styles.productName}>{item.name}</Text>
            <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(item.category) }]}>
              <Text style={styles.categoryText}>
                {item.category.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
          </View>
          <Icon 
            name={getTrendIcon(item.salesTrend)} 
            size={24} 
            color={getTrendColor(item.salesTrend)} 
          />
        </View>

        <View style={styles.productStats}>
          <View style={styles.statRow}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{item.totalSold}</Text>
              <Text style={styles.statLabel}>Sold ({item.unitOfMeasure})</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatCurrency(item.totalRevenue)}</Text>
              <Text style={styles.statLabel}>Revenue</Text>
            </View>
          </View>

          <View style={styles.statRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: item.profit >= 0 ? '#28a745' : '#dc3545' }]}>
                {formatCurrency(item.profit)}
              </Text>
              <Text style={styles.statLabel}>Profit</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: item.profitMargin >= 0 ? '#28a745' : '#dc3545' }]}>
                {item.profitMargin.toFixed(1)}%
              </Text>
              <Text style={styles.statLabel}>Margin</Text>
            </View>
          </View>

          <View style={styles.statRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: item.currentStock < 10 ? '#dc3545' : '#333' }]}>
                {item.currentStock}
              </Text>
              <Text style={styles.statLabel}>Stock</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatCurrency(item.averageSellingPrice)}</Text>
              <Text style={styles.statLabel}>Avg. Price</Text>
            </View>
          </View>

          {item.lastSaleDate && (
            <View style={styles.lastActivity}>
              <Text style={styles.lastActivityText}>
                Last sold: {formatDate(item.lastSaleDate)}
              </Text>
            </View>
          )}
        </View>
      </Card>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderPeriodSelector()}
        {renderOverviewStats()}
        {renderFiltersAndSort()}
        
        <Card style={styles.listCard}>
          <View style={styles.listHeader}>
            <Text style={styles.sectionTitle}>
              Product Statistics ({filteredStats.length})
            </Text>
          </View>
          
          <FlatList
            data={filteredStats}
            keyExtractor={(item) => item.id}
            renderItem={renderProductItem}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  periodCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  periodButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#e9ecef',
    borderWidth: 1,
    borderColor: '#dee2e6',
    alignItems: 'center',
  },
  activePeriodButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  activePeriodButtonText: {
    color: 'white',
  },
  filtersCard: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  filterRow: {
    marginBottom: 12,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#e9ecef',
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  activeFilterButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  activeFilterButtonText: {
    color: 'white',
  },
  overviewCard: {
    marginBottom: 16,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  overviewItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  overviewValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
    marginBottom: 4,
  },
  overviewLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  alertContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    padding: 8,
    backgroundColor: '#fff3cd',
    borderRadius: 6,
  },
  alertText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#856404',
  },
  listCard: {
    marginBottom: 16,
  },
  listHeader: {
    marginBottom: 16,
  },
  productCard: {
    marginBottom: 12,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  productTitleContainer: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  categoryText: {
    fontSize: 10,
    color: 'white',
    fontWeight: 'bold',
  },
  productStats: {
    gap: 8,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  lastActivity: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  lastActivityText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  separator: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 8,
  },
});

export default ProductStatisticsScreen;