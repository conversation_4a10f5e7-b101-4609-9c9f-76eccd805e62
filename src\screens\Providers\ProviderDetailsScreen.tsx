import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Image,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Provider, Product, ProviderStackParamList } from '@/types';
import { makePhoneCall, sendSMS, openWhatsApp, openEmail, openWebsite, formatDate } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ProviderDetailsProps = StackScreenProps<ProviderStackParamList, 'ProviderDetails'>;
type ProviderDetailsNavigationProp = StackNavigationProp<ProviderStackParamList, 'ProviderDetails'>;

const ProviderDetailsScreen: React.FC<ProviderDetailsProps> = () => {
  const navigation = useNavigation<ProviderDetailsNavigationProp>();
  const route = useRoute<ProviderDetailsProps['route']>();
  const { providerId } = route.params;

  const [provider, setProvider] = useState<Provider | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadProviderDetails();
  }, [providerId]);

  const loadProviderDetails = async () => {
    try {
      setLoading(true);
      
      const [providerData, allProducts] = await Promise.all([
        db.getProviderById(providerId),
        db.getAllProducts(),
      ]);

      if (!providerData) {
        Alert.alert('Error', 'Provider not found', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
        return;
      }

      setProvider(providerData);
      
      // Filter products for this provider
      const providerProducts = allProducts.filter(product =>
        product.providers.includes(providerId)
      );
      setProducts(providerProducts);
      
    } catch (error) {
      console.error('Error loading provider details:', error);
      Alert.alert('Error', 'Failed to load provider details');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('ProviderForm', { providerId });
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Provider',
      `Are you sure you want to delete "${provider?.firstName} ${provider?.lastName || ''}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteProvider(providerId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete provider');
            }
          },
        },
      ]
    );
  };

  const handleContact = (method: 'call' | 'sms' | 'whatsapp' | 'email' | 'website') => {
    if (!provider) return;

    const primaryPhone = provider.phoneNumbers.find(p => p.isPrimary) || provider.phoneNumbers[0];

    switch (method) {
      case 'call':
        if (primaryPhone) {
          makePhoneCall(primaryPhone.number);
        } else {
          Alert.alert('Error', 'No phone number available');
        }
        break;
      case 'sms':
        if (primaryPhone) {
          sendSMS(primaryPhone.number);
        } else {
          Alert.alert('Error', 'No phone number available');
        }
        break;
      case 'whatsapp':
        const whatsappPhone = provider.phoneNumbers.find(p => p.hasWhatsApp);
        if (whatsappPhone) {
          openWhatsApp(whatsappPhone.number);
        } else {
          Alert.alert('Error', 'WhatsApp not available for this provider');
        }
        break;
      case 'email':
        if (provider.email) {
          openEmail(provider.email);
        } else {
          Alert.alert('Error', 'No email address available');
        }
        break;
      case 'website':
        if (provider.website) {
          openWebsite(provider.website);
        } else {
          Alert.alert('Error', 'No website available');
        }
        break;
    }
  };

  if (loading) {
    return <LoadingSpinner text="Loading provider details..." />;
  }

  if (!provider) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error" size={64} color="#ccc" />
          <Text style={styles.errorText}>Provider not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Provider Image */}
        {provider.photo ? (
          <Image source={{ uri: provider.photo }} style={styles.providerImage} />
        ) : (
          <View style={styles.placeholderImage}>
            <Icon name="business" size={64} color="#ccc" />
          </View>
        )}

        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={styles.providerName}>
            {provider.firstName} {provider.lastName || ''}
          </Text>
          
          {provider.companyName && (
            <View style={styles.infoRow}>
              <Icon name="business" size={20} color="#666" />
              <Text style={styles.infoText}>{provider.companyName}</Text>
            </View>
          )}

          {provider.address && (
            <View style={styles.infoRow}>
              <Icon name="location-on" size={20} color="#666" />
              <Text style={styles.infoText}>{provider.address}</Text>
            </View>
          )}

          <View style={styles.infoRow}>
            <Icon name="today" size={20} color="#666" />
            <Text style={styles.infoText}>
              Added on {formatDate(new Date(provider.createdAt))}
            </Text>
          </View>
        </Card>

        {/* Contact Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Information</Text>
          
          {provider.phoneNumbers.map((phone, index) => (
            <View key={index} style={styles.contactRow}>
              <View style={styles.contactInfo}>
                <View style={styles.phoneRow}>
                  <Text style={styles.phoneNumber}>{phone.number}</Text>
                  {phone.isPrimary && (
                    <View style={styles.primaryBadge}>
                      <Text style={styles.primaryText}>Primary</Text>
                    </View>
                  )}
                  {phone.hasWhatsApp && (
                    <View style={styles.whatsappBadge}>
                      <Text style={styles.whatsappText}>WhatsApp</Text>
                    </View>
                  )}
                </View>
                <View style={styles.contactActions}>
                  <TouchableOpacity
                    style={styles.contactButton}
                    onPress={() => handleContact('call')}
                  >
                    <Icon name="phone" size={16} color="#4CAF50" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.contactButton}
                    onPress={() => handleContact('sms')}
                  >
                    <Icon name="sms" size={16} color="#2196F3" />
                  </TouchableOpacity>
                  {phone.hasWhatsApp && (
                    <TouchableOpacity
                      style={styles.contactButton}
                      onPress={() => handleContact('whatsapp')}
                    >
                      <Icon name="chat" size={16} color="#25D366" />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>
          ))}
          
          {provider.email && (
            <TouchableOpacity
              style={styles.infoRow}
              onPress={() => handleContact('email')}
            >
              <Icon name="email" size={20} color="#2196F3" />
              <Text style={[styles.infoText, styles.linkText]}>
                {provider.email}
              </Text>
            </TouchableOpacity>
          )}
          
          {provider.website && (
            <TouchableOpacity
              style={styles.infoRow}
              onPress={() => handleContact('website')}
            >
              <Icon name="language" size={20} color="#2196F3" />
              <Text style={[styles.infoText, styles.linkText]}>
                {provider.website}
              </Text>
            </TouchableOpacity>
          )}
        </Card>

        {/* Notes */}
        {provider.notes && (
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <Text style={styles.notesText}>{provider.notes}</Text>
          </Card>
        )}

        {/* Products */}
        {products.length > 0 && (
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Products ({products.length})</Text>
            {products.map((product) => (
              <View key={product.id} style={styles.productCard}>
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productDetails}>
                  {product.wholesaleQuantity} • {product.unitOfMeasure}
                </Text>
              </View>
            ))}
          </Card>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Edit Provider"
            onPress={handleEdit}
            variant="primary"
            style={styles.actionButton}
          />
          <Button
            title="Delete Provider"
            onPress={handleDelete}
            variant="danger"
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  providerImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  placeholderImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    margin: 16,
    marginBottom: 8,
  },
  providerName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
    flex: 1,
    lineHeight: 24,
  },
  linkText: {
    color: '#2196F3',
    textDecorationLine: 'underline',
  },
  contactRow: {
    marginBottom: 12,
  },
  contactInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  phoneNumber: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  primaryBadge: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  primaryText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '500',
  },
  whatsappBadge: {
    backgroundColor: '#25D366',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 4,
  },
  whatsappText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '500',
  },
  contactActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactButton: {
    padding: 8,
    marginLeft: 4,
  },
  notesText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  productCard: {
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginBottom: 8,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  productDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginTop: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    color: '#999',
    marginTop: 16,
  },
});

export default ProviderDetailsScreen;