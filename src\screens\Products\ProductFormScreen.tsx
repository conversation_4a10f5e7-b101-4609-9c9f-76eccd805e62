import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Product, Provider, ProductStackParamList } from '@/types';
import { generateId } from '@/utils/helpers';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ProductFormProps = StackScreenProps<ProductStackParamList, 'ProductForm'>;

interface ProductForm {
  name: string;
  photo?: string;
  salableIndividually: boolean;
  wholesaleQuantity: string;
  unitOfMeasure: string;
  manufacturerName?: string;
  manufacturerCountry?: string;
  manufacturerAddress?: string;
  manufacturerPhone?: string;
  manufacturerEmail?: string;
  manufacturerWebsite?: string;
  storageConsiderations?: string;
  additionalInformation?: string;
  providerIds: string[];
}

const UNITS_OF_MEASURE = [
  'kg', 'g', 'lb', 'oz',
  'L', 'ml', 'gal', 'fl oz',
  'pcs', 'dozen', 'box', 'bag',
  'meter', 'cm', 'ft', 'inch',
  'pack', 'bundle', 'case', 'carton'
];

const ProductFormScreen: React.FC<ProductFormProps> = () => {
  const navigation = useNavigation();
  const route = useRoute<ProductFormProps['route']>();
  const { productId } = route.params || {};

  const isEditing = !!productId;

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [form, setForm] = useState<ProductForm>({
    name: '',
    salableIndividually: true,
    wholesaleQuantity: '',
    unitOfMeasure: 'kg',
    providerIds: [],
  });
  const [errors, setErrors] = useState<Partial<ProductForm>>({});

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      const [allProviders, productData] = await Promise.all([
        db.getAllProviders(),
        isEditing ? db.getProductById(productId!) : Promise.resolve(null),
      ]);

      setProviders(allProviders);

      if (productData) {
        setForm({
          name: productData.name,
          photo: productData.photo,
          salableIndividually: productData.salableIndividually,
          wholesaleQuantity: productData.wholesaleQuantity,
          unitOfMeasure: productData.unitOfMeasure,
          manufacturerName: productData.manufacturer?.name,
          manufacturerCountry: productData.manufacturer?.country,
          manufacturerAddress: productData.manufacturer?.address,
          manufacturerPhone: productData.manufacturer?.phone,
          manufacturerEmail: productData.manufacturer?.email,
          manufacturerWebsite: productData.manufacturer?.website,
          storageConsiderations: productData.storageConsiderations,
          additionalInformation: productData.additionalInformation,
          providerIds: productData.providers,
        });
      }
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const updateForm = <K extends keyof ProductForm>(field: K, value: ProductForm[K]) => {
    setForm(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ProductForm> = {};

    if (!form.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!form.wholesaleQuantity.trim()) {
      newErrors.wholesaleQuantity = 'Wholesale quantity is required';
    }

    if (!form.unitOfMeasure) {
      newErrors.unitOfMeasure = 'Unit of measure is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);

      const productData: Product = {
        id: isEditing ? productId! : generateId(),
        name: form.name.trim(),
        photo: form.photo,
        salableIndividually: form.salableIndividually,
        wholesaleQuantity: form.wholesaleQuantity.trim(),
        unitOfMeasure: form.unitOfMeasure,
        manufacturer: {
          name: form.manufacturerName?.trim(),
          country: form.manufacturerCountry?.trim(),
          address: form.manufacturerAddress?.trim(),
          phone: form.manufacturerPhone?.trim(),
          email: form.manufacturerEmail?.trim(),
          website: form.manufacturerWebsite?.trim(),
        },
        storageConsiderations: form.storageConsiderations?.trim(),
        additionalInformation: form.additionalInformation?.trim(),
        providers: form.providerIds,
        createdAt: isEditing ? new Date() : new Date(),
        updatedAt: new Date(),
      };

      // Remove empty manufacturer object if all fields are empty
      const hasManufacturerData = Object.values(productData.manufacturer || {}).some(val => val && val.trim());
      if (!hasManufacturerData) {
        productData.manufacturer = undefined;
      }

      if (isEditing) {
        await db.updateProduct(productData);
      } else {
        await db.createProduct(productData);
      }

      Alert.alert(
        'Success',
        `Product ${isEditing ? 'updated' : 'created'} successfully`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error saving product:', error);
      Alert.alert('Error', 'Failed to save product');
    } finally {
      setSaving(false);
    }
  };

  const toggleProvider = (providerId: string) => {
    const newProviderIds = form.providerIds.includes(providerId)
      ? form.providerIds.filter(id => id !== providerId)
      : [...form.providerIds, providerId];
    
    updateForm('providerIds', newProviderIds);
  };

  if (loading) {
    return <LoadingSpinner text="Loading..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          <Input
            label="Product Name"
            value={form.name}
            onChangeText={(value) => updateForm('name', value)}
            error={errors.name}
            placeholder="Enter product name"
            required
          />

          <Input
            label="Wholesale Quantity"
            value={form.wholesaleQuantity}
            onChangeText={(value) => updateForm('wholesaleQuantity', value)}
            error={errors.wholesaleQuantity}
            placeholder="e.g., 50kg bags, 10 boxes"
            required
          />

          <View style={styles.pickerContainer}>
            <Text style={styles.pickerLabel}>Unit of Measure *</Text>
            <View style={styles.picker}>
              <Picker
                selectedValue={form.unitOfMeasure}
                onValueChange={(value) => updateForm('unitOfMeasure', value)}
              >
                {UNITS_OF_MEASURE.map(unit => (
                  <Picker.Item key={unit} label={unit} value={unit} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Can be sold individually</Text>
            <Switch
              value={form.salableIndividually}
              onValueChange={(value) => updateForm('salableIndividually', value)}
              trackColor={{ false: '#ccc', true: '#2196F3' }}
              thumbColor={form.salableIndividually ? '#fff' : '#f4f3f4'}
            />
          </View>
        </Card>

        {/* Manufacturer Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Manufacturer (Optional)</Text>
          
          <Input
            label="Manufacturer Name"
            value={form.manufacturerName}
            onChangeText={(value) => updateForm('manufacturerName', value)}
            placeholder="Enter manufacturer name"
          />

          <Input
            label="Country"
            value={form.manufacturerCountry}
            onChangeText={(value) => updateForm('manufacturerCountry', value)}
            placeholder="Enter country"
          />

          <Input
            label="Address"
            value={form.manufacturerAddress}
            onChangeText={(value) => updateForm('manufacturerAddress', value)}
            placeholder="Enter address"
            multiline
            numberOfLines={3}
          />

          <Input
            label="Phone Number"
            value={form.manufacturerPhone}
            onChangeText={(value) => updateForm('manufacturerPhone', value)}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
          />

          <Input
            label="Email"
            value={form.manufacturerEmail}
            onChangeText={(value) => updateForm('manufacturerEmail', value)}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <Input
            label="Website"
            value={form.manufacturerWebsite}
            onChangeText={(value) => updateForm('manufacturerWebsite', value)}
            placeholder="Enter website URL"
            autoCapitalize="none"
          />
        </Card>

        {/* Storage and Additional Info */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Additional Information</Text>
          
          <Input
            label="Storage Considerations"
            value={form.storageConsiderations}
            onChangeText={(value) => updateForm('storageConsiderations', value)}
            placeholder="e.g., Store in cool, dry place"
            multiline
            numberOfLines={3}
          />

          <Input
            label="Additional Information"
            value={form.additionalInformation}
            onChangeText={(value) => updateForm('additionalInformation', value)}
            placeholder="Any additional details about the product"
            multiline
            numberOfLines={3}
          />
        </Card>

        {/* Providers */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Providers</Text>
          <Text style={styles.sectionDescription}>
            Select which providers supply this product
          </Text>
          
          {providers.length === 0 ? (
            <View style={styles.emptyProviders}>
              <Icon name="business" size={32} color="#ccc" />
              <Text style={styles.emptyText}>No providers available</Text>
              <Text style={styles.emptySubtext}>
                Add providers first to associate them with products
              </Text>
            </View>
          ) : (
            providers.map((provider) => (
              <TouchableOpacity
                key={provider.id}
                style={styles.providerItem}
                onPress={() => toggleProvider(provider.id)}
              >
                <View style={styles.providerInfo}>
                  <Text style={styles.providerName}>
                    {provider.firstName} {provider.lastName || ''}
                  </Text>
                  {provider.companyName && (
                    <Text style={styles.companyName}>{provider.companyName}</Text>
                  )}
                </View>
                <View style={styles.checkbox}>
                  {form.providerIds.includes(provider.id) && (
                    <Icon name="check" size={20} color="#2196F3" />
                  )}
                </View>
              </TouchableOpacity>
            ))
          )}
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Cancel"
            onPress={() => navigation.goBack()}
            variant="secondary"
            style={styles.actionButton}
          />
          <Button
            title={isEditing ? 'Update Product' : 'Create Product'}
            onPress={handleSave}
            loading={saving}
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  picker: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  emptyProviders: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#ccc',
    textAlign: 'center',
    marginTop: 4,
  },
  providerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  companyName: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default ProductFormScreen;