import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  FlatList,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Client, Sale, Product, ClientStackParamList } from '@/types';
import { makePhoneCall, sendSMS, formatDate, formatCurrency } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ClientDetailsProps = StackScreenProps<ClientStackParamList, 'ClientDetails'>;
type ClientDetailsNavigationProp = StackNavigationProp<ClientStackParamList, 'ClientDetails'>;

const ClientDetailsScreen: React.FC<ClientDetailsProps> = () => {
  const navigation = useNavigation<ClientDetailsNavigationProp>();
  const route = useRoute<ClientDetailsProps['route']>();
  const { clientId } = route.params;

  const [client, setClient] = useState<Client | null>(null);
  const [sales, setSales] = useState<Sale[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalPurchases, setTotalPurchases] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadClientDetails();
  }, [clientId]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadClientDetails();
    });
    return unsubscribe;
  }, [navigation]);

  const loadClientDetails = async () => {
    try {
      setLoading(true);
      
      const [clientData, allSales, allProducts] = await Promise.all([
        db.getClientById(clientId),
        db.getAllSales(),
        db.getAllProducts(),
      ]);

      if (!clientData) {
        Alert.alert('Error', 'Client not found', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
        return;
      }

      setClient(clientData);
      setProducts(allProducts);
      
      // Filter sales for this client
      const clientSales = allSales.filter(sale => sale.clientId === clientId);
      setSales(clientSales);
      
      // Calculate totals
      const totalPurchaseCount = clientSales.length;
      const totalAmountSpent = clientSales.reduce((sum, sale) => sum + sale.totalAmount, 0);
      
      setTotalPurchases(totalPurchaseCount);
      setTotalAmount(totalAmountSpent);
    } catch (error) {
      console.error('Error loading client details:', error);
      Alert.alert('Error', 'Failed to load client details');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('ClientForm', { clientId });
  };

  const handleDelete = () => {
    if (!client) return;
    
    Alert.alert(
      'Delete Client',
      `Are you sure you want to delete "${client.firstName} ${client.lastName || ''}"?\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteClient(clientId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete client');
            }
          },
        },
      ]
    );
  };

  const handleContact = (method: 'call' | 'sms') => {
    if (!client) return;
    
    if (method === 'call') {
      makePhoneCall(client.phoneNumber);
    } else {
      sendSMS(client.phoneNumber);
    }
  };

  const getProductName = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'Unknown Product';
  };

  const renderSaleItem = ({ item }: { item: Sale }) => (
    <Card style={styles.saleCard}>
      <View style={styles.saleHeader}>
        <Text style={styles.saleDate}>{formatDate(new Date(item.date))}</Text>
        <Text style={styles.saleAmount}>{formatCurrency(item.totalAmount)}</Text>
      </View>
      
      <View style={styles.saleItems}>
        {item.items.map((saleItem, index) => (
          <View key={index} style={styles.saleItemRow}>
            <Text style={styles.productName}>{getProductName(saleItem.productId)}</Text>
            <Text style={styles.quantity}>Qty: {saleItem.quantity}</Text>
            <Text style={styles.itemPrice}>{formatCurrency(saleItem.totalPrice)}</Text>
          </View>
        ))}
      </View>
      
      <View style={styles.saleFooter}>
        <View style={styles.receiptStatus}>
          <Icon 
            name={item.receiptStatus === 'issued' ? 'receipt' : 'receipt-long'} 
            size={14} 
            color={item.receiptStatus === 'issued' ? '#4CAF50' : '#FF9800'} 
          />
          <Text style={[styles.receiptText, 
            { color: item.receiptStatus === 'issued' ? '#4CAF50' : '#FF9800' }
          ]}>
            Receipt {item.receiptStatus === 'issued' ? 'Issued' : 'Not Issued'}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderEmptyPurchases = () => (
    <View style={styles.emptyState}>
      <Icon name="shopping-cart" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>No Purchases Yet</Text>
      <Text style={styles.emptyDescription}>
        This client hasn't made any purchases yet.
      </Text>
    </View>
  );

  if (loading) {
    return <LoadingSpinner text="Loading client details..." />;
  }

  if (!client) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Client not found</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Client Information */}
        <Card style={styles.clientInfoCard}>
          <View style={styles.clientHeader}>
            <View style={styles.clientInfo}>
              <Text style={styles.clientName}>
                {client.firstName} {client.lastName || ''}
              </Text>
              <Text style={styles.phoneNumber}>{client.phoneNumber}</Text>
              {client.note && (
                <Text style={styles.clientNote}>{client.note}</Text>
              )}
              <Text style={styles.joinedDate}>
                Client since {formatDate(new Date(client.createdAt))}
              </Text>
            </View>
          </View>
          
          {/* Contact Actions */}
          <View style={styles.contactActions}>
            <TouchableOpacity
              style={[styles.contactButton, styles.callButton]}
              onPress={() => handleContact('call')}
            >
              <Icon name="phone" size={20} color="#fff" />
              <Text style={styles.contactButtonText}>Call</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.contactButton, styles.smsButton]}
              onPress={() => handleContact('sms')}
            >
              <Icon name="sms" size={20} color="#fff" />
              <Text style={styles.contactButtonText}>SMS</Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Purchase Statistics */}
        <Card style={styles.statsCard}>
          <Text style={styles.statsTitle}>Purchase Summary</Text>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{totalPurchases}</Text>
              <Text style={styles.statLabel}>Total Purchases</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatCurrency(totalAmount)}</Text>
              <Text style={styles.statLabel}>Total Spent</Text>
            </View>
          </View>
        </Card>

        {/* Purchase History */}
        <Card style={styles.historyCard}>
          <View style={styles.historyHeader}>
            <Text style={styles.historyTitle}>Purchase History</Text>
            <Text style={styles.historyCount}>({sales.length} orders)</Text>
          </View>
          
          {sales.length > 0 ? (
            <FlatList
              data={sales.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())}
              renderItem={renderSaleItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            renderEmptyPurchases()
          )}
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          title="Edit Client"
          onPress={handleEdit}
          style={styles.editButton}
          textStyle={styles.editButtonText}
        />
        <Button
          title="Delete"
          onPress={handleDelete}
          style={styles.deleteButton}
          textStyle={styles.deleteButtonText}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorText: {
    fontSize: 18,
    color: '#999',
  },
  clientInfoCard: {
    margin: 16,
    marginBottom: 8,
  },
  clientHeader: {
    marginBottom: 16,
  },
  clientInfo: {
    marginBottom: 16,
  },
  clientName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  phoneNumber: {
    fontSize: 16,
    color: '#2196F3',
    marginBottom: 4,
  },
  clientNote: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  joinedDate: {
    fontSize: 12,
    color: '#999',
  },
  contactActions: {
    flexDirection: 'row',
    gap: 12,
  },
  contactButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  callButton: {
    backgroundColor: '#4CAF50',
  },
  smsButton: {
    backgroundColor: '#2196F3',
  },
  contactButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  statsCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 16,
  },
  historyCard: {
    marginHorizontal: 16,
    marginBottom: 80,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  historyCount: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  saleCard: {
    marginBottom: 8,
    backgroundColor: '#fafafa',
  },
  saleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  saleDate: {
    fontSize: 14,
    color: '#666',
  },
  saleAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  saleItems: {
    marginBottom: 12,
  },
  saleItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  productName: {
    flex: 2,
    fontSize: 14,
    color: '#333',
  },
  quantity: {
    flex: 1,
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  itemPrice: {
    fontSize: 14,
    color: '#333',
    textAlign: 'right',
    minWidth: 80,
  },
  saleFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  receiptStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  receiptText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#ccc',
    textAlign: 'center',
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  editButton: {
    flex: 1,
    backgroundColor: '#FF9800',
  },
  editButtonText: {
    color: '#fff',
  },
  deleteButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 24,
  },
  deleteButtonText: {
    color: '#fff',
  },
});

export default ClientDetailsScreen;