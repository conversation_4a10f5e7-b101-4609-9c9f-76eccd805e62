import { Product, Provider, Client, Acquisition, Sale, Debt, AppSettings } from '@/types';

// Mock Products
export const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Premium Basmati Rice',
    photo: undefined,
    salableIndividually: false,
    wholesaleQuantity: '50kg bags',
    unitOfMeasure: 'kg',
    manufacturer: {
      name: 'Golden Grains Ltd',
      country: 'India',
      address: 'Mumbai, Maharashtra',
      phone: '+91-22-1234567',
      email: '<EMAIL>',
      website: 'www.goldengrains.com'
    },
    storageConsiderations: 'Store in cool, dry place away from moisture',
    additionalInformation: 'Premium quality aged basmati rice',
    providers: ['1', '2'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    name: 'Whole Wheat Flour',
    photo: undefined,
    salableIndividually: true,
    wholesaleQuantity: '25kg sacks',
    unitOfMeasure: 'kg',
    manufacturer: {
      name: 'Fresh Mills Co',
      country: 'Canada',
      address: 'Toronto, ON'
    },
    storageConsiderations: 'Keep in airtight containers',
    additionalInformation: 'Stone ground whole wheat flour',
    providers: ['1'],
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: '3',
    name: 'Organic Lentils',
    photo: undefined,
    salableIndividually: true,
    wholesaleQuantity: '20kg bags',
    unitOfMeasure: 'kg',
    manufacturer: {
      name: 'Organic Farms',
      country: 'India'
    },
    storageConsiderations: 'Store in dry place',
    additionalInformation: 'Certified organic red lentils',
    providers: ['2'],
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12')
  }
];

// Mock Providers
export const mockProviders: Provider[] = [
  {
    id: '1',
    firstName: 'Ahmed',
    lastName: 'Hassan',
    phoneNumbers: [
      { number: '******-0123', hasWhatsApp: true, isPrimary: true },
      { number: '******-0124', hasWhatsApp: false, isPrimary: false }
    ],
    email: '<EMAIL>',
    photo: undefined,
    website: 'www.grainsinc.com',
    companyName: 'Premium Grains Inc',
    address: '123 Wholesale St, Business District, NY 10001',
    notes: 'Reliable supplier, always delivers on time',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: '2',
    firstName: 'Maria',
    lastName: 'Rodriguez',
    phoneNumbers: [
      { number: '******-0567', hasWhatsApp: true, isPrimary: true }
    ],
    email: '<EMAIL>',
    photo: undefined,
    companyName: 'Organic Supply Co',
    address: '456 Green Ave, Organic Plaza, CA 90210',
    notes: 'Specializes in organic products',
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-08')
  }
];

// Mock Clients
export const mockClients: Client[] = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Smith',
    phoneNumber: '******-1001',
    note: 'Regular customer, prefers weekly deliveries',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: '2',
    firstName: 'Sarah',
    lastName: 'Johnson',
    phoneNumber: '******-1002',
    note: 'Bulk buyer for restaurant chain',
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: '3',
    firstName: 'Mike',
    phoneNumber: '******-1003',
    note: 'Small grocery store owner',
    createdAt: new Date('2024-01-22'),
    updatedAt: new Date('2024-01-22')
  }
];

// Mock Acquisitions
export const mockAcquisitions: Acquisition[] = [
  {
    id: '1',
    date: new Date('2024-01-25'),
    items: [
      {
        productId: '1',
        quantity: 100,
        pricePerUnit: 25.50,
        totalPrice: 2550.00
      },
      {
        productId: '2',
        quantity: 50,
        pricePerUnit: 18.00,
        totalPrice: 900.00
      }
    ],
    providerId: '1',
    isDelivered: true,
    isPaid: true,
    totalAmount: 3450.00,
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-01-25')
  },
  {
    id: '2',
    date: new Date('2024-01-26'),
    items: [
      {
        productId: '3',
        quantity: 30,
        pricePerUnit: 22.00,
        totalPrice: 660.00
      }
    ],
    providerId: '2',
    isDelivered: false,
    expectedDeliveryDate: new Date('2024-01-28'),
    deliveryNotes: 'Expected delivery by evening',
    isPaid: false,
    totalAmount: 660.00,
    createdAt: new Date('2024-01-26'),
    updatedAt: new Date('2024-01-26')
  }
];

// Mock Sales
export const mockSales: Sale[] = [
  {
    id: '1',
    date: new Date('2024-01-27'),
    items: [
      {
        productId: '1',
        quantity: 10,
        pricePerUnit: 30.00,
        totalPrice: 300.00
      },
      {
        productId: '2',
        quantity: 5,
        pricePerUnit: 22.00,
        totalPrice: 110.00
      }
    ],
    clientId: '1',
    receiptStatus: 'issued',
    totalAmount: 410.00,
    createdAt: new Date('2024-01-27'),
    updatedAt: new Date('2024-01-27')
  },
  {
    id: '2',
    date: new Date('2024-01-28'),
    items: [
      {
        productId: '3',
        quantity: 8,
        pricePerUnit: 26.00,
        totalPrice: 208.00
      }
    ],
    clientId: '2',
    receiptStatus: 'not_issued',
    totalAmount: 208.00,
    createdAt: new Date('2024-01-28'),
    updatedAt: new Date('2024-01-28')
  }
];

// Mock Debts
export const mockDebts: Debt[] = [
  {
    id: '1',
    debtorId: '2',
    amount: 208.00,
    dateOfDebt: new Date('2024-01-28'),
    dueDate: new Date('2024-02-15'),
    notes: 'Payment due in 30 days',
    saleId: '2',
    isPaid: false,
    reminderSettings: {
      enabled: true,
      customReminders: [],
      dailyReminderEnabled: true
    },
    createdAt: new Date('2024-01-28'),
    updatedAt: new Date('2024-01-28')
  }
];

// Mock App Settings
export const mockAppSettings: AppSettings = {
  userFirstName: '',
  shopName: '',
  preferredLanguage: 'en',
  country: 'US',
  currency: 'USD',
  isFirstUse: true,
  createdAt: new Date(),
  updatedAt: new Date()
};