# Setup Guide - Wholesale Shop Management App

## 🚀 Development Environment Setup

This guide will walk you through setting up the development environment for the Wholesale Shop Management Application.

## 📋 Prerequisites

### System Requirements

**For Windows:**
- Windows 10 (version 1903 or higher)
- 16 GB RAM minimum (32 GB recommended)
- 10 GB free disk space

**For macOS:**
- macOS 10.15 Catalina or higher
- 16 GB RAM minimum (32 GB recommended)
- 10 GB free disk space
- Xcode 12 or higher (for iOS development)

**For Linux:**
- Ubuntu 18.04 LTS or equivalent
- 16 GB RAM minimum
- 10 GB free disk space

### Required Software

1. **Node.js** (v18.0.0 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **npm** or **Yarn** (Package Manager)
   - npm comes with Node.js
   - Yarn installation: `npm install -g yarn`

3. **React Native CLI**
   ```bash
   npm install -g @react-native-community/cli
   ```

4. **Git** (Version Control)
   - Download from: https://git-scm.com/

## 🤖 Android Development Setup

### 1. Install Java Development Kit (JDK)

**Windows/Linux:**
```bash
# Install OpenJDK 11
# Windows: Download from https://adoptopenjdk.net/
# Linux:
sudo apt-get install openjdk-11-jdk
```

**macOS:**
```bash
brew install openjdk@11
```

### 2. Install Android Studio

1. Download Android Studio from: https://developer.android.com/studio
2. Install with default settings
3. Open Android Studio and complete the setup wizard

### 3. Configure Android SDK

1. Open Android Studio
2. Go to **Tools** → **SDK Manager**
3. Install the following:
   - **Android SDK Platform 31** (API Level 31)
   - **Android SDK Build-Tools 31.0.0**
   - **Android SDK Platform-Tools**
   - **Android SDK Tools**
   - **Intel x86 Atom_64 System Images** (for emulator)

### 4. Set Environment Variables

**Windows:**
```cmd
setx ANDROID_HOME "%LOCALAPPDATA%\Android\Sdk"
setx PATH "%PATH%;%LOCALAPPDATA%\Android\Sdk\platform-tools"
```

**macOS/Linux:**
```bash
echo 'export ANDROID_HOME=$HOME/Library/Android/sdk' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/emulator' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools/bin' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/platform-tools' >> ~/.bashrc
source ~/.bashrc
```

### 5. Create Android Virtual Device (AVD)

1. Open Android Studio
2. Go to **Tools** → **AVD Manager**
3. Click **Create Virtual Device**
4. Select **Phone** → **Pixel 4**
5. Select **API Level 31** system image
6. Click **Finish**

## 🍎 iOS Development Setup (macOS Only)

### 1. Install Xcode

1. Download Xcode from Mac App Store
2. Install Xcode Command Line Tools:
   ```bash
   xcode-select --install
   ```

### 2. Install CocoaPods

```bash
sudo gem install cocoapods
```

### 3. Install iOS Simulator

1. Open Xcode
2. Go to **Xcode** → **Preferences** → **Components**
3. Install latest iOS Simulator

## 📱 Project Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd wholesale-shop-app
```

### 2. Install Dependencies

```bash
# Using npm
npm install

# Using yarn
yarn install
```

### 3. iOS Specific Setup (macOS only)

```bash
cd ios
pod install
cd ..
```

### 4. Environment Configuration

Create environment files if needed:

```bash
# Create .env file
touch .env
```

Add environment variables:
```env
# .env
API_URL=https://api.example.com
DEBUG_MODE=true
```

## 🔧 Development Workflow

### Starting the Development Server

1. **Start Metro Bundler:**
   ```bash
   npm start
   # or
   yarn start
   ```

2. **Run on Android:**
   ```bash
   # Start Android emulator first or connect physical device
   npm run android
   # or
   yarn android
   ```

3. **Run on iOS (macOS only):**
   ```bash
   npm run ios
   # or
   yarn ios
   ```

### Debugging

#### React Native Debugger

1. Install React Native Debugger:
   ```bash
   # macOS
   brew install --cask react-native-debugger
   
   # Windows/Linux
   # Download from: https://github.com/jhen0409/react-native-debugger/releases
   ```

2. Enable debugging in app:
   - Android: Shake device or `Ctrl+M` in emulator
   - iOS: Shake device or `Cmd+D` in simulator
   - Select "Debug with Chrome" or "Open Debugger"

#### Flipper (Recommended)

1. Install Flipper:
   - Download from: https://fbflipper.com/

2. Configure in your app (already configured in this project)

### Code Quality Tools

#### ESLint and Prettier

```bash
# Run ESLint
npm run lint
# or
yarn lint

# Fix ESLint issues
npm run lint:fix
# or
yarn lint:fix

# Format code with Prettier
npm run format
# or
yarn format
```

#### TypeScript

```bash
# Type checking
npm run tsc
# or
yarn tsc
```

## 🧪 Testing Setup

### Unit Testing with Jest

```bash
# Run tests
npm test
# or
yarn test

# Run tests with coverage
npm run test:coverage
# or
yarn test:coverage
```

### E2E Testing with Detox

1. Install Detox CLI:
   ```bash
   npm install -g detox-cli
   ```

2. Build for testing:
   ```bash
   # iOS
   detox build --configuration ios.sim.debug
   
   # Android
   detox build --configuration android.emu.debug
   ```

3. Run E2E tests:
   ```bash
   # iOS
   detox test --configuration ios.sim.debug
   
   # Android
   detox test --configuration android.emu.debug
   ```

## 📦 Building for Production

### Android Release Build

1. **Generate Signed APK:**
   ```bash
   cd android
   ./gradlew assembleRelease
   ```

2. **Generate App Bundle (AAB):**
   ```bash
   cd android
   ./gradlew bundleRelease
   ```

### iOS Release Build

1. **Archive in Xcode:**
   - Open `ios/WholesaleShopApp.xcworkspace` in Xcode
   - Select **Product** → **Archive**
   - Follow App Store submission process

## 🔐 Code Signing Setup

### Android Signing

1. **Generate Keystore:**
   ```bash
   keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
   ```

2. **Configure in `android/gradle.properties`:**
   ```properties
   MYAPP_RELEASE_STORE_FILE=my-release-key.keystore
   MYAPP_RELEASE_KEY_ALIAS=my-key-alias
   MYAPP_RELEASE_STORE_PASSWORD=*****
   MYAPP_RELEASE_KEY_PASSWORD=*****
   ```

### iOS Signing

1. **Apple Developer Account:**
   - Register at: https://developer.apple.com/

2. **Configure in Xcode:**
   - Open project in Xcode
   - Select target → **Signing & Capabilities**
   - Select your development team
   - Configure bundle identifier

## 🚀 Deployment

### Google Play Store (Android)

1. **Create Google Play Console Account:**
   - Register at: https://play.google.com/console/

2. **Upload App Bundle:**
   - Create new app in console
   - Upload AAB file
   - Complete store listing
   - Submit for review

### Apple App Store (iOS)

1. **App Store Connect:**
   - Upload app via Xcode or Application Loader
   - Complete app information
   - Submit for review

## 📊 Performance Monitoring

### Metro Bundle Analyzer

```bash
# Analyze bundle size
npx react-native-bundle-visualizer
```

### Performance Profiling

1. **Enable Flipper Performance Plugin**
2. **Use React DevTools Profiler**
3. **Monitor with Xcode Instruments (iOS)**
4. **Use Android Studio Profiler (Android)**

## 🔧 Troubleshooting

### Common Issues

#### Metro Bundler Issues

```bash
# Clear Metro cache
npx react-native start --reset-cache

# Clear node modules and reinstall
rm -rf node_modules
npm install
```

#### Android Build Issues

```bash
# Clean gradle
cd android
./gradlew clean

# Clear React Native cache
npx react-native start --reset-cache
```

#### iOS Build Issues

```bash
# Clean build folder
cd ios
xcodebuild clean

# Reinstall pods
pod deintegrate
pod install
```

### Environment Issues

#### Node Version Conflicts

```bash
# Install nvm (Node Version Manager)
# macOS/Linux:
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.38.0/install.sh | bash

# Use specific Node version
nvm install 18.0.0
nvm use 18.0.0
```

#### Port Conflicts

```bash
# Kill process on port 8081 (Metro)
# macOS/Linux:
lsof -ti:8081 | xargs kill -9

# Windows:
netstat -ano | findstr :8081
taskkill /PID <PID> /F
```

## 📚 Additional Resources

### Documentation
- React Native: https://reactnative.dev/docs/getting-started
- React Navigation: https://reactnavigation.org/docs/getting-started
- AsyncStorage: https://github.com/react-native-async-storage/async-storage

### Tools
- React Native CLI: https://github.com/react-native-community/cli
- Flipper: https://fbflipper.com/docs/getting-started/
- Reactotron: https://github.com/infinitered/reactotron

### Communities
- React Native Community: https://github.com/react-native-community
- Stack Overflow: https://stackoverflow.com/questions/tagged/react-native
- Discord: React Native Community Discord Server

## 🔧 Development Tips

### Productivity Enhancements

1. **VS Code Extensions:**
   - React Native Tools
   - ES7+ React/Redux/React-Native snippets
   - Prettier - Code formatter
   - ESLint
   - Auto Rename Tag

2. **Shell Aliases:**
   ```bash
   # Add to ~/.bashrc or ~/.zshrc
   alias rn='react-native'
   alias rnios='react-native run-ios'
   alias rnand='react-native run-android'
   alias rnstart='react-native start'
   ```

3. **Emulator Quick Start:**
   ```bash
   # Android
   emulator -avd Pixel_4_API_31

   # iOS
   xcrun simctl boot "iPhone 13"
   ```

### Code Organization Best Practices

1. **Folder Structure:**
   ```
   src/
   ├── components/     # Reusable components
   ├── screens/        # Screen components
   ├── navigation/     # Navigation configuration
   ├── services/       # API and business logic
   ├── utils/          # Helper functions
   ├── types/          # TypeScript definitions
   └── assets/         # Images, fonts, etc.
   ```

2. **Naming Conventions:**
   - PascalCase for components: `ProductCard.tsx`
   - camelCase for functions: `formatCurrency()`
   - UPPER_CASE for constants: `API_BASE_URL`

3. **Import Organization:**
   ```typescript
   // 1. React and React Native
   import React from 'react';
   import { View, Text } from 'react-native';
   
   // 2. Third-party libraries
   import { useNavigation } from '@react-navigation/native';
   
   // 3. Internal imports
   import DatabaseService from '@/services/database';
   import { Product } from '@/types';
   ```

## 📞 Support

### Getting Help

1. **Check Documentation:** Review this guide and API documentation
2. **Search Issues:** Check repository issues for similar problems
3. **Community Support:** Ask questions in React Native communities
4. **Direct Support:** Contact development <NAME_EMAIL>

### Reporting Bugs

When reporting bugs, include:
- Device/OS version
- React Native version
- Node.js version
- Steps to reproduce
- Error messages
- Screenshots if applicable

---

*Last updated: January 2025*
*Setup Guide Version: 1.0.0*