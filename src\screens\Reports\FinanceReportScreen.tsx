import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  Share,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DateTimePicker from '@react-native-community/datetimepicker';
import DatabaseService from '@/services/database';
import { Sale, Acquisition, Product, FinanceReport } from '@/types';
import { formatCurrency, formatDate } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface PeriodStats {
  totalSales: number;
  totalRevenue: number;
  totalAcquisitions: number;
  totalCosts: number;
  grossProfit: number;
  averageSaleValue: number;
  topProducts: Array<{
    id: string;
    name: string;
    quantity: number;
    revenue: number;
  }>;
  salesByDay: Array<{
    date: string;
    sales: number;
    revenue: number;
  }>;
  profitMargin: number;
}

type PeriodType = 'week' | 'month' | 'quarter' | 'year' | 'custom';

const FinanceReportScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<PeriodStats | null>(null);
  const [period, setPeriod] = useState<PeriodType>('month');
  const [customStartDate, setCustomStartDate] = useState<Date>(new Date());
  const [customEndDate, setCustomEndDate] = useState<Date>(new Date());
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadFinanceData();
  }, [period, customStartDate, customEndDate]);

  const getPeriodDates = (): { start: Date; end: Date } => {
    const now = new Date();
    const start = new Date();
    
    switch (period) {
      case 'week':
        start.setDate(now.getDate() - 7);
        break;
      case 'month':
        start.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        start.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        start.setFullYear(now.getFullYear() - 1);
        break;
      case 'custom':
        return { start: customStartDate, end: customEndDate };
      default:
        start.setMonth(now.getMonth() - 1);
    }
    
    return { start, end: now };
  };

  const loadFinanceData = async () => {
    try {
      setLoading(true);
      
      const [sales, acquisitions, products] = await Promise.all([
        db.getAllSales(),
        db.getAllAcquisitions(),
        db.getAllProducts(),
      ]);

      const { start, end } = getPeriodDates();
      
      // Filter data by period
      const periodSales = sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= start && saleDate <= end;
      });
      
      const periodAcquisitions = acquisitions.filter(acq => {
        const acqDate = new Date(acq.date);
        return acqDate >= start && acqDate <= end;
      });

      // Calculate basic stats
      const totalSales = periodSales.length;
      const totalRevenue = periodSales.reduce((sum, sale) => sum + sale.totalAmount, 0);
      const totalAcquisitions = periodAcquisitions.length;
      const totalCosts = periodAcquisitions.reduce((sum, acq) => sum + acq.totalAmount, 0);
      const grossProfit = totalRevenue - totalCosts;
      const averageSaleValue = totalSales > 0 ? totalRevenue / totalSales : 0;
      const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

      // Calculate top products
      const productSales: { [key: string]: { quantity: number; revenue: number } } = {};
      
      periodSales.forEach(sale => {
        sale.items.forEach(item => {
          if (!productSales[item.productId]) {
            productSales[item.productId] = { quantity: 0, revenue: 0 };
          }
          productSales[item.productId].quantity += item.quantity;
          productSales[item.productId].revenue += item.totalPrice;
        });
      });

      const topProducts = Object.entries(productSales)
        .map(([productId, data]) => {
          const product = products.find(p => p.id === productId);
          return {
            id: productId,
            name: product?.name || 'Unknown Product',
            quantity: data.quantity,
            revenue: data.revenue,
          };
        })
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5);

      // Calculate daily sales for trend analysis
      const salesByDay: { [key: string]: { sales: number; revenue: number } } = {};
      
      periodSales.forEach(sale => {
        const dateKey = formatDate(new Date(sale.date));
        if (!salesByDay[dateKey]) {
          salesByDay[dateKey] = { sales: 0, revenue: 0 };
        }
        salesByDay[dateKey].sales += 1;
        salesByDay[dateKey].revenue += sale.totalAmount;
      });

      const salesByDayArray = Object.entries(salesByDay)
        .map(([date, data]) => ({
          date,
          sales: data.sales,
          revenue: data.revenue,
        }))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      setStats({
        totalSales,
        totalRevenue,
        totalAcquisitions,
        totalCosts,
        grossProfit,
        averageSaleValue,
        topProducts,
        salesByDay: salesByDayArray,
        profitMargin,
      });
      
    } catch (error) {
      console.error('Error loading finance data:', error);
      Alert.alert('Error', 'Failed to load finance data');
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async () => {
    if (!stats) return;

    const { start, end } = getPeriodDates();
    const periodName = period === 'custom' 
      ? `${formatDate(start)} to ${formatDate(end)}`
      : period.charAt(0).toUpperCase() + period.slice(1);

    const report = `
Finance Report - ${periodName}
Generated on: ${formatDate(new Date())}

SUMMARY
${'-'.repeat(40)}
Total Sales: ${stats.totalSales}
Total Revenue: ${formatCurrency(stats.totalRevenue)}
Total Acquisitions: ${stats.totalAcquisitions}
Total Costs: ${formatCurrency(stats.totalCosts)}
Gross Profit: ${formatCurrency(stats.grossProfit)}
Profit Margin: ${stats.profitMargin.toFixed(1)}%
Average Sale Value: ${formatCurrency(stats.averageSaleValue)}

TOP PRODUCTS BY REVENUE
${'-'.repeat(40)}
${stats.topProducts.map((product, index) => 
  `${index + 1}. ${product.name}
     Quantity Sold: ${product.quantity}
     Revenue: ${formatCurrency(product.revenue)}`
).join('\n\n')}

DAILY BREAKDOWN
${'-'.repeat(40)}
${stats.salesByDay.map(day => 
  `${day.date}: ${day.sales} sales - ${formatCurrency(day.revenue)}`
).join('\n')}
    `.trim();

    try {
      await Share.share({
        message: report,
        title: `Finance Report - ${periodName}`,
      });
    } catch (error) {
      console.error('Error sharing report:', error);
      Alert.alert('Error', 'Failed to export report');
    }
  };

  const renderPeriodSelector = () => (
    <Card style={styles.periodCard}>
      <Text style={styles.sectionTitle}>Report Period</Text>
      <View style={styles.periodButtons}>
        {(['week', 'month', 'quarter', 'year', 'custom'] as PeriodType[]).map((p) => (
          <TouchableOpacity
            key={p}
            style={[
              styles.periodButton,
              period === p && styles.activePeriodButton,
            ]}
            onPress={() => setPeriod(p)}
          >
            <Text
              style={[
                styles.periodButtonText,
                period === p && styles.activePeriodButtonText,
              ]}
            >
              {p.charAt(0).toUpperCase() + p.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {period === 'custom' && (
        <View style={styles.customDateContainer}>
          <View style={styles.dateInputRow}>
            <View style={styles.dateInput}>
              <Text style={styles.dateLabel}>Start Date</Text>
              <TouchableOpacity
                style={styles.dateSelector}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Text style={styles.dateText}>{formatDate(customStartDate)}</Text>
                <Icon name="event" size={20} color="#666" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.dateInput}>
              <Text style={styles.dateLabel}>End Date</Text>
              <TouchableOpacity
                style={styles.dateSelector}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Text style={styles.dateText}>{formatDate(customEndDate)}</Text>
                <Icon name="event" size={20} color="#666" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </Card>
  );

  const renderSummaryCards = () => {
    if (!stats) return null;

    return (
      <View style={styles.summaryGrid}>
        <Card style={styles.summaryCard}>
          <Icon name="trending-up" size={24} color="#28a745" />
          <Text style={styles.summaryValue}>{formatCurrency(stats.totalRevenue)}</Text>
          <Text style={styles.summaryLabel}>Total Revenue</Text>
        </Card>

        <Card style={styles.summaryCard}>
          <Icon name="trending-down" size={24} color="#dc3545" />
          <Text style={styles.summaryValue}>{formatCurrency(stats.totalCosts)}</Text>
          <Text style={styles.summaryLabel}>Total Costs</Text>
        </Card>

        <Card style={[styles.summaryCard, styles.fullWidth]}>
          <Icon 
            name="account-balance-wallet" 
            size={24} 
            color={stats.grossProfit >= 0 ? "#28a745" : "#dc3545"} 
          />
          <Text 
            style={[
              styles.summaryValue, 
              { color: stats.grossProfit >= 0 ? "#28a745" : "#dc3545" }
            ]}
          >
            {formatCurrency(stats.grossProfit)}
          </Text>
          <Text style={styles.summaryLabel}>Gross Profit</Text>
          <Text style={styles.profitMargin}>
            {stats.profitMargin.toFixed(1)}% Margin
          </Text>
        </Card>
      </View>
    );
  };

  const renderMetrics = () => {
    if (!stats) return null;

    return (
      <Card style={styles.metricsCard}>
        <Text style={styles.sectionTitle}>Key Metrics</Text>
        
        <View style={styles.metricRow}>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>{stats.totalSales}</Text>
            <Text style={styles.metricLabel}>Total Sales</Text>
          </View>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>{stats.totalAcquisitions}</Text>
            <Text style={styles.metricLabel}>Total Acquisitions</Text>
          </View>
        </View>

        <View style={styles.metricRow}>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>{formatCurrency(stats.averageSaleValue)}</Text>
            <Text style={styles.metricLabel}>Average Sale Value</Text>
          </View>
          <View style={styles.metricItem}>
            <Text style={[
              styles.metricValue, 
              { color: stats.profitMargin >= 0 ? "#28a745" : "#dc3545" }
            ]}>
              {stats.profitMargin.toFixed(1)}%
            </Text>
            <Text style={styles.metricLabel}>Profit Margin</Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderTopProducts = () => {
    if (!stats || stats.topProducts.length === 0) return null;

    return (
      <Card style={styles.topProductsCard}>
        <Text style={styles.sectionTitle}>Top Products by Revenue</Text>
        
        {stats.topProducts.map((product, index) => (
          <View key={product.id} style={styles.productItem}>
            <View style={styles.productRank}>
              <Text style={styles.rankNumber}>{index + 1}</Text>
            </View>
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{product.name}</Text>
              <Text style={styles.productStats}>
                {product.quantity} units • {formatCurrency(product.revenue)}
              </Text>
            </View>
          </View>
        ))}
      </Card>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderPeriodSelector()}
        {renderSummaryCards()}
        {renderMetrics()}
        {renderTopProducts()}
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Export Report"
          onPress={exportReport}
          style={styles.exportButton}
          icon="share"
        />
      </View>

      {showStartDatePicker && (
        <DateTimePicker
          value={customStartDate}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowStartDatePicker(false);
            if (selectedDate) {
              setCustomStartDate(selectedDate);
            }
          }}
          maximumDate={customEndDate}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={customEndDate}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowEndDatePicker(false);
            if (selectedDate) {
              setCustomEndDate(selectedDate);
            }
          }}
          minimumDate={customStartDate}
          maximumDate={new Date()}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  periodCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  periodButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#e9ecef',
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  activePeriodButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  activePeriodButtonText: {
    color: 'white',
  },
  customDateContainer: {
    marginTop: 16,
  },
  dateInputRow: {
    flexDirection: 'row',
    gap: 12,
  },
  dateInput: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: 16,
  },
  fullWidth: {
    minWidth: '100%',
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  profitMargin: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    fontStyle: 'italic',
  },
  metricsCard: {
    marginBottom: 16,
  },
  metricRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  metricItem: {
    flex: 1,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  topProductsCard: {
    marginBottom: 16,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  productRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  rankNumber: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  productStats: {
    fontSize: 14,
    color: '#666',
  },
  footer: {
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  exportButton: {
    backgroundColor: '#28a745',
  },
});

export default FinanceReportScreen;