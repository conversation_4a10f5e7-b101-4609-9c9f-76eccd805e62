import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  FlatList,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Acquisition, Provider, Product, AcquisitionStackParamList } from '@/types';
import { formatDate, formatCurrency, makePhoneCall, sendSMS, openWhatsApp, openEmail } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type AcquisitionDetailsProps = StackScreenProps<AcquisitionStackParamList, 'AcquisitionDetails'>;
type AcquisitionDetailsNavigationProp = StackNavigationProp<AcquisitionStackParamList, 'AcquisitionDetails'>;

const AcquisitionDetailsScreen: React.FC<AcquisitionDetailsProps> = () => {
  const navigation = useNavigation<AcquisitionDetailsNavigationProp>();
  const route = useRoute<AcquisitionDetailsProps['route']>();
  const { acquisitionId } = route.params;

  const [acquisition, setAcquisition] = useState<Acquisition | null>(null);
  const [provider, setProvider] = useState<Provider | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadAcquisitionDetails();
  }, [acquisitionId]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadAcquisitionDetails();
    });
    return unsubscribe;
  }, [navigation]);

  const loadAcquisitionDetails = async () => {
    try {
      setLoading(true);
      
      const [acquisitionData, allProducts] = await Promise.all([
        db.getAcquisitionById(acquisitionId),
        db.getAllProducts(),
      ]);

      if (!acquisitionData) {
        Alert.alert('Error', 'Acquisition not found', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
        return;
      }

      setAcquisition(acquisitionData);
      setProducts(allProducts);
      
      // Load provider information if exists
      if (acquisitionData.providerId) {
        const providerData = await db.getProviderById(acquisitionData.providerId);
        setProvider(providerData);
      }
    } catch (error) {
      console.error('Error loading acquisition details:', error);
      Alert.alert('Error', 'Failed to load acquisition details');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('AcquisitionForm', { acquisitionId });
  };

  const handleDelete = () => {
    if (!acquisition) return;
    
    Alert.alert(
      'Delete Acquisition',
      `Are you sure you want to delete this acquisition of ${formatCurrency(acquisition.totalAmount)}?\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteAcquisition(acquisitionId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete acquisition');
            }
          },
        },
      ]
    );
  };

  const handleToggleDeliveryStatus = async () => {
    if (!acquisition) return;
    
    const newStatus = !acquisition.isDelivered;
    const statusText = newStatus ? 'delivered' : 'pending';
    
    Alert.alert(
      'Update Delivery Status',
      `Mark this acquisition as ${statusText}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Update',
          onPress: async () => {
            try {
              const updatedAcquisition = {
                ...acquisition,
                isDelivered: newStatus,
                updatedAt: new Date(),
              };
              await db.updateAcquisition(updatedAcquisition);
              await loadAcquisitionDetails();
            } catch (error) {
              Alert.alert('Error', 'Failed to update delivery status');
            }
          },
        },
      ]
    );
  };

  const handleTogglePaymentStatus = async () => {
    if (!acquisition) return;
    
    const newStatus = !acquisition.isPaid;
    const statusText = newStatus ? 'paid' : 'unpaid';
    
    Alert.alert(
      'Update Payment Status',
      `Mark this acquisition as ${statusText}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Update',
          onPress: async () => {
            try {
              const updatedAcquisition = {
                ...acquisition,
                isPaid: newStatus,
                updatedAt: new Date(),
              };
              await db.updateAcquisition(updatedAcquisition);
              await loadAcquisitionDetails();
            } catch (error) {
              Alert.alert('Error', 'Failed to update payment status');
            }
          },
        },
      ]
    );
  };

  const handleContactProvider = (method: 'call' | 'sms' | 'whatsapp' | 'email') => {
    if (!provider) return;
    
    const primaryPhone = provider.phoneNumbers.find(p => p.isPrimary)?.number || provider.phoneNumbers[0]?.number;
    
    switch (method) {
      case 'call':
        if (primaryPhone) makePhoneCall(primaryPhone);
        break;
      case 'sms':
        if (primaryPhone) sendSMS(primaryPhone);
        break;
      case 'whatsapp':
        const whatsappPhone = provider.phoneNumbers.find(p => p.hasWhatsApp)?.number;
        if (whatsappPhone) openWhatsApp(whatsappPhone);
        break;
      case 'email':
        if (provider.email) openEmail(provider.email);
        break;
    }
  };

  const getProductName = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'Unknown Product';
  };

  const getProductUnit = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.unitOfMeasure || '';
  };

  const getDeliveryStatusColor = (isDelivered: boolean) => {
    return isDelivered ? '#4CAF50' : '#FF9800';
  };

  const getPaymentStatusColor = (isPaid: boolean) => {
    return isPaid ? '#4CAF50' : '#F44336';
  };

  const renderAcquisitionItem = ({ item, index }: { item: any; index: number }) => (
    <Card style={styles.itemCard}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemNumber}>#{index + 1}</Text>
        <Text style={styles.itemTotal}>{formatCurrency(item.totalPrice)}</Text>
      </View>
      
      <Text style={styles.productName}>{getProductName(item.productId)}</Text>
      
      <View style={styles.itemDetails}>
        <View style={styles.itemDetailRow}>
          <Text style={styles.itemDetailLabel}>Quantity:</Text>
          <Text style={styles.itemDetailValue}>
            {item.quantity} {getProductUnit(item.productId)}
          </Text>
        </View>
        <View style={styles.itemDetailRow}>
          <Text style={styles.itemDetailLabel}>Unit Price:</Text>
          <Text style={styles.itemDetailValue}>
            {formatCurrency(item.pricePerUnit)}
          </Text>
        </View>
        <View style={styles.itemDetailRow}>
          <Text style={styles.itemDetailLabel}>Total:</Text>
          <Text style={[styles.itemDetailValue, styles.itemTotalValue]}>
            {formatCurrency(item.totalPrice)}
          </Text>
        </View>
      </View>
    </Card>
  );

  if (loading) {
    return <LoadingSpinner text="Loading acquisition details..." />;
  }

  if (!acquisition) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Acquisition not found</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Acquisition Header */}
        <Card style={styles.headerCard}>
          <View style={styles.acquisitionHeader}>
            <View style={styles.acquisitionInfo}>
              <Text style={styles.acquisitionId}>Order #{acquisition.id.slice(-8).toUpperCase()}</Text>
              <Text style={styles.acquisitionDate}>{formatDate(new Date(acquisition.date))}</Text>
              <Text style={styles.createdDate}>
                Created {formatDate(new Date(acquisition.createdAt))}
              </Text>
            </View>
            <View style={styles.totalContainer}>
              <Text style={styles.totalAmount}>{formatCurrency(acquisition.totalAmount)}</Text>
              <View style={styles.statusContainer}>
                <View style={styles.status}>
                  <Icon 
                    name={acquisition.isDelivered ? 'local-shipping' : 'schedule'} 
                    size={14} 
                    color={getDeliveryStatusColor(acquisition.isDelivered)} 
                  />
                  <Text style={[styles.statusText, { color: getDeliveryStatusColor(acquisition.isDelivered) }]}>
                    {acquisition.isDelivered ? 'Delivered' : 'Pending'}
                  </Text>
                </View>
                <View style={styles.status}>
                  <Icon 
                    name={acquisition.isPaid ? 'payment' : 'money-off'} 
                    size={14} 
                    color={getPaymentStatusColor(acquisition.isPaid)} 
                  />
                  <Text style={[styles.statusText, { color: getPaymentStatusColor(acquisition.isPaid) }]}>
                    {acquisition.isPaid ? 'Paid' : 'Unpaid'}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </Card>

        {/* Provider Information */}
        <Card style={styles.providerCard}>
          <Text style={styles.sectionTitle}>Provider Information</Text>
          {provider ? (
            <View>
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>
                  {provider.firstName} {provider.lastName || ''}
                </Text>
                {provider.companyName && (
                  <Text style={styles.companyName}>{provider.companyName}</Text>
                )}
                <Text style={styles.providerPhone}>
                  {provider.phoneNumbers.find(p => p.isPrimary)?.number || provider.phoneNumbers[0]?.number}
                </Text>
                {provider.email && (
                  <Text style={styles.providerEmail}>{provider.email}</Text>
                )}
              </View>
              
              <View style={styles.contactActions}>
                <TouchableOpacity
                  style={[styles.contactButton, styles.callButton]}
                  onPress={() => handleContactProvider('call')}
                >
                  <Icon name="phone" size={16} color="#fff" />
                  <Text style={styles.contactButtonText}>Call</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.contactButton, styles.smsButton]}
                  onPress={() => handleContactProvider('sms')}
                >
                  <Icon name="sms" size={16} color="#fff" />
                  <Text style={styles.contactButtonText}>SMS</Text>
                </TouchableOpacity>
                
                {provider.phoneNumbers.some(p => p.hasWhatsApp) && (
                  <TouchableOpacity
                    style={[styles.contactButton, styles.whatsappButton]}
                    onPress={() => handleContactProvider('whatsapp')}
                  >
                    <Icon name="chat" size={16} color="#fff" />
                    <Text style={styles.contactButtonText}>WhatsApp</Text>
                  </TouchableOpacity>
                )}
                
                {provider.email && (
                  <TouchableOpacity
                    style={[styles.contactButton, styles.emailButton]}
                    onPress={() => handleContactProvider('email')}
                  >
                    <Icon name="email" size={16} color="#fff" />
                    <Text style={styles.contactButtonText}>Email</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          ) : (
            <View style={styles.noProvider}>
              <Icon name="business" size={24} color="#999" />
              <Text style={styles.noProviderText}>No Provider Assigned</Text>
              <Text style={styles.noProviderSubtext}>No provider information recorded</Text>
            </View>
          )}
        </Card>

        {/* Delivery Information */}
        {!acquisition.isDelivered && acquisition.expectedDeliveryDate && (
          <Card style={styles.deliveryCard}>
            <Text style={styles.sectionTitle}>Delivery Information</Text>
            <View style={styles.deliveryInfo}>
              <Icon name="schedule" size={20} color="#FF9800" />
              <View style={styles.deliveryDetails}>
                <Text style={styles.deliveryLabel}>Expected Delivery:</Text>
                <Text style={styles.deliveryDate}>
                  {formatDate(new Date(acquisition.expectedDeliveryDate))}
                </Text>
                {acquisition.deliveryNotes && (
                  <Text style={styles.deliveryNotes}>{acquisition.deliveryNotes}</Text>
                )}
              </View>
            </View>
          </Card>
        )}

        {/* Acquisition Items */}
        <Card style={styles.itemsCard}>
          <View style={styles.itemsHeader}>
            <Text style={styles.sectionTitle}>Items Acquired</Text>
            <Text style={styles.itemsCount}>({acquisition.items.length} items)</Text>
          </View>
          
          <FlatList
            data={acquisition.items}
            renderItem={renderAcquisitionItem}
            keyExtractor={(_, index) => index.toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
          
          {/* Acquisition Summary */}
          <View style={styles.summaryContainer}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Items:</Text>
              <Text style={styles.summaryValue}>{acquisition.items.length}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Quantity:</Text>
              <Text style={styles.summaryValue}>
                {acquisition.items.reduce((sum, item) => sum + item.quantity, 0)}
              </Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total Amount:</Text>
              <Text style={styles.totalValue}>{formatCurrency(acquisition.totalAmount)}</Text>
            </View>
          </View>
        </Card>

        {/* Status Actions */}
        <Card style={styles.statusActionsCard}>
          <Text style={styles.sectionTitle}>Status Updates</Text>
          <View style={styles.statusActions}>
            <TouchableOpacity
              style={[
                styles.statusActionButton,
                { backgroundColor: getDeliveryStatusColor(acquisition.isDelivered) + '15' }
              ]}
              onPress={handleToggleDeliveryStatus}
            >
              <Icon 
                name={acquisition.isDelivered ? 'undo' : 'local-shipping'} 
                size={20} 
                color={getDeliveryStatusColor(acquisition.isDelivered)} 
              />
              <Text style={[styles.statusActionText, { color: getDeliveryStatusColor(acquisition.isDelivered) }]}>
                Mark as {acquisition.isDelivered ? 'Pending' : 'Delivered'}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.statusActionButton,
                { backgroundColor: getPaymentStatusColor(acquisition.isPaid) + '15' }
              ]}
              onPress={handleTogglePaymentStatus}
            >
              <Icon 
                name={acquisition.isPaid ? 'undo' : 'payment'} 
                size={20} 
                color={getPaymentStatusColor(acquisition.isPaid)} 
              />
              <Text style={[styles.statusActionText, { color: getPaymentStatusColor(acquisition.isPaid) }]}>
                Mark as {acquisition.isPaid ? 'Unpaid' : 'Paid'}
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          title="Edit Acquisition"
          onPress={handleEdit}
          style={styles.editButton}
          textStyle={styles.editButtonText}
        />
        <Button
          title="Delete"
          onPress={handleDelete}
          style={styles.deleteButton}
          textStyle={styles.deleteButtonText}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorText: {
    fontSize: 18,
    color: '#999',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
  },
  acquisitionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  acquisitionInfo: {
    flex: 1,
  },
  acquisitionId: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  acquisitionDate: {
    fontSize: 16,
    color: '#666',
    marginBottom: 2,
  },
  createdDate: {
    fontSize: 12,
    color: '#999',
  },
  totalContainer: {
    alignItems: 'flex-end',
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  statusContainer: {
    alignItems: 'flex-end',
    gap: 4,
  },
  status: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  providerCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  providerInfo: {
    marginBottom: 16,
  },
  providerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  companyName: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  providerPhone: {
    fontSize: 14,
    color: '#2196F3',
    marginBottom: 4,
  },
  providerEmail: {
    fontSize: 14,
    color: '#666',
  },
  contactActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  callButton: {
    backgroundColor: '#4CAF50',
  },
  smsButton: {
    backgroundColor: '#2196F3',
  },
  whatsappButton: {
    backgroundColor: '#25D366',
  },
  emailButton: {
    backgroundColor: '#FF9800',
  },
  contactButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 12,
  },
  noProvider: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  noProviderText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#999',
    marginTop: 8,
    marginBottom: 4,
  },
  noProviderSubtext: {
    fontSize: 12,
    color: '#ccc',
  },
  deliveryCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  deliveryDetails: {
    flex: 1,
  },
  deliveryLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  deliveryDate: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF9800',
    marginBottom: 4,
  },
  deliveryNotes: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  itemsCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  itemsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  itemsCount: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  itemCard: {
    marginBottom: 8,
    backgroundColor: '#fafafa',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemNumber: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
    backgroundColor: '#e0e0e0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  itemDetails: {
    gap: 4,
  },
  itemDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemDetailLabel: {
    fontSize: 14,
    color: '#666',
  },
  itemDetailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  itemTotalValue: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  summaryContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statusActionsCard: {
    marginHorizontal: 16,
    marginBottom: 80,
  },
  statusActions: {
    gap: 12,
  },
  statusActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 12,
  },
  statusActionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  editButton: {
    flex: 1,
    backgroundColor: '#FF9800',
  },
  editButtonText: {
    color: '#fff',
  },
  deleteButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 24,
  },
  deleteButtonText: {
    color: '#fff',
  },
});

export default AcquisitionDetailsScreen;