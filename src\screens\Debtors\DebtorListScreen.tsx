import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Debt, Client, DebtorStackParamList } from '@/types';
import { formatDate, formatCurrency, makePhoneCall, sendSMS } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type DebtorListNavigationProp = StackNavigationProp<DebtorStackParamList, 'DebtorList'>;

const DebtorListScreen: React.FC = () => {
  const navigation = useNavigation<DebtorListNavigationProp>();
  const [debts, setDebts] = useState<Debt[]>([]);
  const [filteredDebts, setFilteredDebts] = useState<Debt[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'unpaid' | 'overdue'>('unpaid');

  const db = DatabaseService.getInstance();

  useFocusEffect(
    useCallback(() => {
      loadDebts();
    }, [])
  );

  const loadDebts = async () => {
    try {
      setLoading(true);
      const [allDebts, allClients] = await Promise.all([
        db.getAllDebts(),
        db.getAllClients(),
      ]);
      
      setDebts(allDebts);
      setClients(allClients);
      applyFilter(allDebts, filter);
    } catch (error) {
      console.error('Error loading debts:', error);
      Alert.alert('Error', 'Failed to load debts');
    } finally {
      setLoading(false);
    }
  };

  const applyFilter = (allDebts: Debt[], currentFilter: typeof filter) => {
    let filtered = allDebts;
    
    if (currentFilter === 'unpaid') {
      filtered = allDebts.filter(debt => !debt.isPaid);
    } else if (currentFilter === 'overdue') {
      const now = new Date();
      filtered = allDebts.filter(debt => 
        !debt.isPaid && debt.dueDate && new Date(debt.dueDate) < now
      );
    }
    
    setFilteredDebts(filtered);
  };

  const handleFilterChange = (newFilter: typeof filter) => {
    setFilter(newFilter);
    applyFilter(debts, newFilter);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim() === '') {
      applyFilter(debts, filter);
    } else {
      const baseFiltered = filter === 'all' ? debts : 
        filter === 'unpaid' ? debts.filter(debt => !debt.isPaid) :
        debts.filter(debt => !debt.isPaid && debt.dueDate && new Date(debt.dueDate) < new Date());
      
      const searchFiltered = baseFiltered.filter(debt => {
        const debtor = getDebtorName(debt.debtorId);
        const amountStr = formatCurrency(debt.amount);
        const dateStr = formatDate(new Date(debt.dateOfDebt));
        
        return (
          debtor.toLowerCase().includes(query.toLowerCase()) ||
          amountStr.includes(query) ||
          dateStr.toLowerCase().includes(query.toLowerCase()) ||
          debt.notes?.toLowerCase().includes(query.toLowerCase())
        );
      });
      setFilteredDebts(searchFiltered);
    }
  };

  const handleDeleteDebt = (debt: Debt) => {
    Alert.alert(
      'Delete Debt',
      `Are you sure you want to delete this debt of ${formatCurrency(debt.amount)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteDebt(debt.id);
              await loadDebts();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete debt');
            }
          },
        },
      ]
    );
  };

  const handleMarkAsPaid = async (debt: Debt) => {
    Alert.alert(
      'Mark as Paid',
      `Mark debt of ${formatCurrency(debt.amount)} as paid?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Mark Paid',
          onPress: async () => {
            try {
              const updatedDebt = { ...debt, isPaid: true, updatedAt: new Date() };
              await db.updateDebt(updatedDebt);
              await loadDebts();
            } catch (error) {
              Alert.alert('Error', 'Failed to update debt');
            }
          },
        },
      ]
    );
  };

  const handleContactDebtor = (debtorId: string, method: 'call' | 'sms') => {
    const client = clients.find(c => c.id === debtorId);
    if (!client) {
      Alert.alert('Error', 'Client information not found');
      return;
    }
    
    if (method === 'call') {
      makePhoneCall(client.phoneNumber);
    } else {
      sendSMS(client.phoneNumber, `Hi ${client.firstName}, this is a reminder about your outstanding debt. Please contact us to arrange payment. Thank you.`);
    }
  };

  const getDebtorName = (debtorId: string): string => {
    const client = clients.find(c => c.id === debtorId);
    return client ? `${client.firstName} ${client.lastName || ''}`.trim() : 'Unknown Debtor';
  };

  const getDebtorPhone = (debtorId: string): string => {
    const client = clients.find(c => c.id === debtorId);
    return client?.phoneNumber || '';
  };

  const isOverdue = (debt: Debt): boolean => {
    if (!debt.dueDate || debt.isPaid) return false;
    return new Date(debt.dueDate) < new Date();
  };

  const getDaysOverdue = (debt: Debt): number => {
    if (!debt.dueDate || debt.isPaid) return 0;
    const now = new Date();
    const dueDate = new Date(debt.dueDate);
    if (dueDate >= now) return 0;
    return Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
  };

  const renderDebt = ({ item }: { item: Debt }) => {
    const overdue = isOverdue(item);
    const daysOverdue = getDaysOverdue(item);
    
    return (
      <Card
        style={[
          styles.debtCard,
          overdue && styles.overdueCard,
          item.isPaid && styles.paidCard
        ]}
        onPress={() => navigation.navigate('DebtorDetails', { debtId: item.id })}
      >
        <View style={styles.debtHeader}>
          <View style={styles.debtInfo}>
            <Text style={styles.debtorName}>{getDebtorName(item.debtorId)}</Text>
            <Text style={styles.debtorPhone}>{getDebtorPhone(item.debtorId)}</Text>
            <Text style={styles.debtDate}>
              Debt from {formatDate(new Date(item.dateOfDebt))}
            </Text>
            {item.dueDate && (
              <Text style={[styles.dueDate, overdue && styles.overdueDueDate]}>
                Due: {formatDate(new Date(item.dueDate))}
                {overdue && ` (${daysOverdue} days overdue)`}
              </Text>
            )}
          </View>
          <View style={styles.debtActions}>
            <View style={styles.amountContainer}>
              <Text style={[styles.debtAmount, item.isPaid && styles.paidAmount]}>
                {formatCurrency(item.amount)}
              </Text>
              {item.isPaid && (
                <View style={styles.paidBadge}>
                  <Icon name="check-circle" size={14} color="#4CAF50" />
                  <Text style={styles.paidText}>Paid</Text>
                </View>
              )}
              {overdue && !item.isPaid && (
                <View style={styles.overdueBadge}>
                  <Icon name="warning" size={14} color="#F44336" />
                  <Text style={styles.overdueText}>Overdue</Text>
                </View>
              )}
            </View>
            <View style={styles.actionButtons}>
              {!item.isPaid && (
                <>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleContactDebtor(item.debtorId, 'call')}
                  >
                    <Icon name="phone" size={18} color="#4CAF50" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleContactDebtor(item.debtorId, 'sms')}
                  >
                    <Icon name="sms" size={18} color="#2196F3" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleMarkAsPaid(item)}
                  >
                    <Icon name="payment" size={18} color="#4CAF50" />
                  </TouchableOpacity>
                </>
              )}
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate('DebtorForm', { debtId: item.id })}
              >
                <Icon name="edit" size={18} color="#FF9800" />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleDeleteDebt(item)}
              >
                <Icon name="delete" size={18} color="#F44336" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        
        {item.notes && (
          <View style={styles.notesContainer}>
            <Text style={styles.notesText}>{item.notes}</Text>
          </View>
        )}
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="account-balance-wallet" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>No Debts Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? 'No debts match your search criteria.'
          : filter === 'unpaid' 
          ? 'No unpaid debts at the moment.'
          : filter === 'overdue'
          ? 'No overdue debts at the moment.'
          : 'No debts recorded yet.'
        }
      </Text>
    </View>
  );

  const calculateTotalDebts = () => {
    return filteredDebts.filter(debt => !debt.isPaid).reduce((sum, debt) => sum + debt.amount, 0);
  };

  const getOverdueCount = () => {
    return filteredDebts.filter(debt => !debt.isPaid && isOverdue(debt)).length;
  };

  if (loading) {
    return <LoadingSpinner text="Loading debts..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Input
          placeholder="Search debts..."
          value={searchQuery}
          onChangeText={handleSearch}
          containerStyle={styles.searchContainer}
          inputStyle={styles.searchInput}
        />
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('DebtorForm', {})}
        >
          <Icon name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterTab, filter === 'all' && styles.activeFilterTab]}
          onPress={() => handleFilterChange('all')}
        >
          <Text style={[styles.filterTabText, filter === 'all' && styles.activeFilterTabText]}>
            All ({debts.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterTab, filter === 'unpaid' && styles.activeFilterTab]}
          onPress={() => handleFilterChange('unpaid')}
        >
          <Text style={[styles.filterTabText, filter === 'unpaid' && styles.activeFilterTabText]}>
            Unpaid ({debts.filter(d => !d.isPaid).length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterTab, filter === 'overdue' && styles.activeFilterTab]}
          onPress={() => handleFilterChange('overdue')}
        >
          <Text style={[styles.filterTabText, filter === 'overdue' && styles.activeFilterTabText]}>
            Overdue ({debts.filter(d => !d.isPaid && isOverdue(d)).length})
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.summaryInfo}>
        <View style={styles.resultInfo}>
          <Text style={styles.resultCount}>
            {filteredDebts.length} debt{filteredDebts.length !== 1 ? 's' : ''}
            {searchQuery && ` found for "${searchQuery}"`}
          </Text>
          {getOverdueCount() > 0 && (
            <Text style={styles.overdueCount}>
              {getOverdueCount()} overdue debt{getOverdueCount() !== 1 ? 's' : ''}
            </Text>
          )}
        </View>
        <View style={styles.totalDebts}>
          <Text style={styles.totalDebtsText}>Outstanding: {formatCurrency(calculateTotalDebts())}</Text>
        </View>
      </View>

      <FlatList
        data={filteredDebts.sort((a, b) => {
          // Sort by overdue first, then by date
          if (isOverdue(a) && !isOverdue(b)) return -1;
          if (!isOverdue(a) && isOverdue(b)) return 1;
          return new Date(b.dateOfDebt).getTime() - new Date(a.dateOfDebt).getTime();
        })}
        renderItem={renderDebt}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  searchContainer: {
    flex: 1,
    marginBottom: 0,
    marginRight: 12,
  },
  searchInput: {
    borderRadius: 25,
    paddingHorizontal: 16,
  },
  addButton: {
    backgroundColor: '#F44336',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  filterContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeFilterTab: {
    borderBottomColor: '#F44336',
  },
  filterTabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: '#F44336',
    fontWeight: 'bold',
  },
  summaryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  resultInfo: {
    flex: 1,
  },
  resultCount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  overdueCount: {
    fontSize: 12,
    color: '#F44336',
    fontWeight: '500',
  },
  totalDebts: {
    alignItems: 'flex-end',
  },
  totalDebtsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#F44336',
  },
  listContent: {
    padding: 8,
    flexGrow: 1,
  },
  debtCard: {
    marginBottom: 8,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  paidCard: {
    opacity: 0.7,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  debtHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  debtInfo: {
    flex: 1,
    marginRight: 12,
  },
  debtorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  debtorPhone: {
    fontSize: 14,
    color: '#2196F3',
    marginBottom: 4,
  },
  debtDate: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  dueDate: {
    fontSize: 12,
    color: '#FF9800',
    fontWeight: '500',
  },
  overdueDueDate: {
    color: '#F44336',
    fontWeight: 'bold',
  },
  debtActions: {
    alignItems: 'flex-end',
  },
  amountContainer: {
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  debtAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#F44336',
    marginBottom: 4,
  },
  paidAmount: {
    color: '#999',
    textDecorationLine: 'line-through',
  },
  paidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  paidText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '500',
  },
  overdueBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  overdueText: {
    fontSize: 12,
    color: '#F44336',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 6,
    marginLeft: 4,
  },
  notesContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    lineHeight: 18,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default DebtorListScreen;