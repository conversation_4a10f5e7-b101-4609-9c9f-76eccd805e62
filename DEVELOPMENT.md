# Development Guide

## Project Overview

This React Native application is designed for wholesale shop management with complete offline functionality. The app follows modern React Native best practices and uses TypeScript for type safety.

## Architecture

### Project Structure
```
src/
├── components/ui/       # Reusable UI components
├── data/               # Static data (countries, mock data)
├── navigation/         # Navigation setup and configuration
├── screens/            # Screen components grouped by feature
├── services/           # Business logic and data management
├── types/              # TypeScript type definitions
└── utils/              # Helper functions and utilities
```

### Key Design Patterns

1. **Service Layer**: All business logic is encapsulated in service classes
2. **Type Safety**: Complete TypeScript coverage with strict mode
3. **Component Composition**: Reusable UI components with consistent interfaces
4. **Navigation**: Stack-based navigation with type-safe parameters
5. **Data Persistence**: AsyncStorage with structured data models

## Development Setup

### Prerequisites
- Node.js 18.x or higher
- React Native CLI
- Android Studio (Android development)
- Xcode (iOS development)
- VS Code with React Native extensions

### Environment Setup
1. Install React Native development environment
2. Set up Android emulator or iOS simulator
3. Install project dependencies
4. Configure vector icons and permissions

### Running the App
```bash
# Install dependencies
npm install

# iOS
npx react-native run-ios

# Android
npx react-native run-android
```

## Core Features Implementation

### Data Management
- **DatabaseService**: Centralized data access layer
- **AsyncStorage**: Local data persistence
- **Mock Data**: Pre-populated sample data for testing
- **Backup/Restore**: Export and import functionality

### Navigation Structure
- **Root Navigator**: Setup vs Main app flow
- **Tab Navigator**: Main app sections
- **Stack Navigators**: Detail views and forms

### UI Components
- **Button**: Configurable with variants and sizes
- **Input**: Form input with validation support
- **Card**: Container component with elevation
- **LoadingSpinner**: Loading states

## Feature Development Guide

### Adding a New Feature

1. **Define Types**
   ```typescript
   // Add to src/types/index.ts
   interface NewFeature {
     id: string;
     name: string;
     // ... other properties
   }
   ```

2. **Create Database Methods**
   ```typescript
   // Add to src/services/database.ts
   async getAllNewFeatures(): Promise<NewFeature[]> {
     return this.getAll<NewFeature>('newFeatures');
   }
   ```

3. **Create UI Components**
   ```typescript
   // src/screens/NewFeature/NewFeatureListScreen.tsx
   const NewFeatureListScreen: React.FC = () => {
     // Implementation
   };
   ```

4. **Add Navigation**
   ```typescript
   // Update navigation types and add routes
   type NewFeatureStackParamList = {
     NewFeatureList: undefined;
     NewFeatureDetails: { id: string };
   };
   ```

### Screen Development Pattern

Each feature typically includes:
- **List Screen**: Display all items with search/filter
- **Details Screen**: Show detailed information
- **Form Screen**: Add/edit functionality

### Database Operations

All database operations use the centralized DatabaseService:
```typescript
const db = DatabaseService.getInstance();

// CRUD operations
await db.createItem(item);
await db.updateItem(item);
await db.deleteItem(id);
const items = await db.getAllItems();
```

## Testing

### Unit Tests
```bash
npm run test
```

### Coverage
```bash
npm run test -- --coverage
```

### Testing Database Operations
```typescript
it('should create and retrieve items', async () => {
  const db = DatabaseService.getInstance();
  const item = { id: '1', name: 'Test' };
  
  await db.createItem(item);
  const retrieved = await db.getItemById('1');
  
  expect(retrieved).toEqual(item);
});
```

## Code Style Guidelines

### TypeScript
- Use strict mode
- Define interfaces for all data structures
- Avoid `any` types
- Use meaningful type names

### React Components
- Use functional components with hooks
- Implement proper error boundaries
- Handle loading states consistently
- Use TypeScript for props interfaces

### Naming Conventions
- **Files**: PascalCase for components, camelCase for utilities
- **Components**: PascalCase
- **Variables**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **Types**: PascalCase with descriptive names

### File Organization
```
ComponentName/
├── ComponentName.tsx       # Main component
├── ComponentName.test.tsx  # Unit tests
├── ComponentName.styles.ts # Styles (if complex)
└── index.ts               # Export barrel
```

## Performance Considerations

### Optimization Strategies
1. **FlatList**: Use for long lists with performance optimization
2. **Memoization**: Use React.memo for expensive components
3. **Image Optimization**: Compress and cache images
4. **Database Queries**: Implement efficient search and filtering

### Memory Management
- Cleanup subscriptions in useEffect
- Avoid memory leaks in navigation
- Optimize AsyncStorage operations

## State Management

### Local State
- Use useState for component-specific state
- Use useEffect for side effects
- Implement custom hooks for reusable logic

### Global State
Currently using service layer pattern. For complex state:
- Consider Redux Toolkit
- Or React Context for simple global state

## Error Handling

### Error Boundaries
```typescript
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    // Log error
    console.error('App Error:', error, errorInfo);
  }
}
```

### Service Error Handling
```typescript
try {
  await db.operation();
} catch (error) {
  console.error('Database error:', error);
  Alert.alert('Error', 'Operation failed');
}
```

## Debugging

### React Native Debugger
- Use React Native Debugger for state inspection
- Enable network inspection
- Use Redux DevTools if applicable

### Console Logging
```typescript
// Development logging
if (__DEV__) {
  console.log('Debug info:', data);
}
```

### Error Reporting
Consider integrating:
- Crashlytics for crash reporting
- Sentry for error tracking

## Build & Deployment

### Development Build
```bash
# Debug build
npx react-native run-android --variant=debug
npx react-native run-ios --configuration Debug
```

### Release Build
```bash
# Release build
npx react-native run-android --variant=release
npx react-native run-ios --configuration Release
```

### Code Signing
- Configure signing certificates
- Set up provisioning profiles (iOS)
- Generate signed APK/AAB (Android)

## Contributing

### Pull Request Process
1. Create feature branch from main
2. Implement feature with tests
3. Update documentation
4. Submit PR with description
5. Code review and approval
6. Merge to main

### Code Review Checklist
- [ ] TypeScript compilation passes
- [ ] Tests pass
- [ ] Code follows style guidelines
- [ ] Performance considerations addressed
- [ ] Documentation updated
- [ ] Error handling implemented

## Future Enhancements

### Planned Features
- Enhanced offline sync
- Image management
- Advanced reporting
- Multi-language support
- Cloud backup integration

### Technical Improvements
- Database optimization
- State management upgrade
- Performance monitoring
- Automated testing pipeline

## Troubleshooting

### Common Issues

1. **Metro bundler issues**
   ```bash
   npx react-native start --reset-cache
   ```

2. **Android build failures**
   ```bash
   cd android && ./gradlew clean
   ```

3. **iOS build failures**
   ```bash
   cd ios && pod install
   ```

4. **TypeScript errors**
   - Check type definitions
   - Verify import statements
   - Clear TypeScript cache

### Debug Commands
```bash
# View device logs
npx react-native log-android
npx react-native log-ios

# Check environment
npx react-native doctor

# Clean project
npx react-native clean
```

## Resources

### Documentation
- [React Native Docs](https://reactnative.dev/docs/getting-started)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React Navigation](https://reactnavigation.org/docs/getting-started)

### Tools
- [React Native Debugger](https://github.com/jhen0409/react-native-debugger)
- [VS Code React Native Tools](https://marketplace.visualstudio.com/items?itemName=msjsdiag.vscode-react-native)
- [Flipper](https://fbflipper.com/)

### Community
- [React Native Community](https://github.com/react-native-community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/react-native)
- [Discord](https://discord.com/invite/0ZcbPKXt5bZjGY5n)