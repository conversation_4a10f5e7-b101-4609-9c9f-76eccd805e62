import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import DatabaseService from '@/services/database';
import { Client, ClientStackParamList } from '@/types';
import { generateId, validatePhoneNumber } from '@/utils/helpers';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ClientFormProps = StackScreenProps<ClientStackParamList, 'ClientForm'>;

interface ClientForm {
  firstName: string;
  lastName?: string;
  phoneNumber: string;
  note?: string;
}

const ClientFormScreen: React.FC<ClientFormProps> = () => {
  const navigation = useNavigation();
  const route = useRoute<ClientFormProps['route']>();
  const { clientId } = route.params || {};

  const isEditing = !!clientId;

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [form, setForm] = useState<ClientForm>({
    firstName: '',
    phoneNumber: '',
  });
  const [errors, setErrors] = useState<Partial<ClientForm>>({});

  const db = DatabaseService.getInstance();

  useEffect(() => {
    if (isEditing) {
      loadClientData();
    }
  }, []);

  const loadClientData = async () => {
    try {
      setLoading(true);
      const clientData = await db.getClientById(clientId!);
      
      if (clientData) {
        setForm({
          firstName: clientData.firstName,
          lastName: clientData.lastName,
          phoneNumber: clientData.phoneNumber,
          note: clientData.note,
        });
      }
    } catch (error) {
      console.error('Error loading client data:', error);
      Alert.alert('Error', 'Failed to load client data');
    } finally {
      setLoading(false);
    }
  };

  const updateForm = <K extends keyof ClientForm>(field: K, value: ClientForm[K]) => {
    setForm(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ClientForm> = {};

    if (!form.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!form.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!validatePhoneNumber(form.phoneNumber)) {
      newErrors.phoneNumber = 'Invalid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);

      const clientData: Client = {
        id: isEditing ? clientId! : generateId(),
        firstName: form.firstName.trim(),
        lastName: form.lastName?.trim(),
        phoneNumber: form.phoneNumber.trim(),
        note: form.note?.trim(),
        createdAt: isEditing ? new Date() : new Date(),
        updatedAt: new Date(),
      };

      if (isEditing) {
        await db.updateClient(clientData);
      } else {
        await db.createClient(clientData);
      }

      Alert.alert(
        'Success',
        `Client ${isEditing ? 'updated' : 'created'} successfully`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error saving client:', error);
      Alert.alert('Error', 'Failed to save client');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="Loading..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Client Information</Text>
          
          <Input
            label="First Name"
            value={form.firstName}
            onChangeText={(value) => updateForm('firstName', value)}
            error={errors.firstName}
            placeholder="Enter first name"
            required
          />

          <Input
            label="Last Name"
            value={form.lastName}
            onChangeText={(value) => updateForm('lastName', value)}
            placeholder="Enter last name"
          />

          <Input
            label="Phone Number"
            value={form.phoneNumber}
            onChangeText={(value) => updateForm('phoneNumber', value)}
            error={errors.phoneNumber}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
            required
          />

          <Input
            label="Note"
            value={form.note}
            onChangeText={(value) => updateForm('note', value)}
            placeholder="Enter any notes about this client"
            multiline
            numberOfLines={4}
          />
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Cancel"
            onPress={() => navigation.goBack()}
            variant="secondary"
            style={styles.actionButton}
          />
          <Button
            title={isEditing ? 'Update Client' : 'Create Client'}
            onPress={handleSave}
            loading={saving}
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default ClientFormScreen;