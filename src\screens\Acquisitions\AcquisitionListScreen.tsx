import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Acquisition, Provider, Product, AcquisitionStackParamList } from '@/types';
import { formatDate, formatCurrency } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type AcquisitionListNavigationProp = StackNavigationProp<AcquisitionStackParamList, 'AcquisitionList'>;

const AcquisitionListScreen: React.FC = () => {
  const navigation = useNavigation<AcquisitionListNavigationProp>();
  const [acquisitions, setAcquisitions] = useState<Acquisition[]>([]);
  const [filteredAcquisitions, setFilteredAcquisitions] = useState<Acquisition[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useFocusEffect(
    useCallback(() => {
      loadAcquisitions();
    }, [])
  );

  const loadAcquisitions = async () => {
    try {
      setLoading(true);
      const [allAcquisitions, allProviders, allProducts] = await Promise.all([
        db.getAllAcquisitions(),
        db.getAllProviders(),
        db.getAllProducts(),
      ]);
      
      setAcquisitions(allAcquisitions);
      setProviders(allProviders);
      setProducts(allProducts);
      setFilteredAcquisitions(allAcquisitions);
    } catch (error) {
      console.error('Error loading acquisitions:', error);
      Alert.alert('Error', 'Failed to load acquisitions');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim() === '') {
      setFilteredAcquisitions(acquisitions);
    } else {
      const filtered = acquisitions.filter(acquisition => {
        const provider = getProviderName(acquisition.providerId);
        const products = acquisition.items.map(item => getProductName(item.productId)).join(' ');
        const dateStr = formatDate(new Date(acquisition.date));
        const amountStr = formatCurrency(acquisition.totalAmount);
        const deliveryStatus = acquisition.isDelivered ? 'delivered' : 'pending';
        const paymentStatus = acquisition.isPaid ? 'paid' : 'unpaid';
        
        return (
          provider.toLowerCase().includes(query.toLowerCase()) ||
          products.toLowerCase().includes(query.toLowerCase()) ||
          dateStr.toLowerCase().includes(query.toLowerCase()) ||
          amountStr.includes(query) ||
          deliveryStatus.includes(query.toLowerCase()) ||
          paymentStatus.includes(query.toLowerCase())
        );
      });
      setFilteredAcquisitions(filtered);
    }
  };

  const handleDeleteAcquisition = (acquisition: Acquisition) => {
    Alert.alert(
      'Delete Acquisition',
      `Are you sure you want to delete this acquisition of ${formatCurrency(acquisition.totalAmount)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteAcquisition(acquisition.id);
              await loadAcquisitions();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete acquisition');
            }
          },
        },
      ]
    );
  };

  const getProviderName = (providerId?: string): string => {
    if (!providerId) return 'No Provider';
    const provider = providers.find(p => p.id === providerId);
    return provider ? `${provider.firstName} ${provider.lastName || ''}`.trim() : 'Unknown Provider';
  };

  const getProductName = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'Unknown Product';
  };

  const getDeliveryStatusColor = (isDelivered: boolean) => {
    return isDelivered ? '#4CAF50' : '#FF9800';
  };

  const getPaymentStatusColor = (isPaid: boolean) => {
    return isPaid ? '#4CAF50' : '#F44336';
  };

  const renderAcquisition = ({ item }: { item: Acquisition }) => (
    <Card
      style={styles.acquisitionCard}
      onPress={() => navigation.navigate('AcquisitionDetails', { acquisitionId: item.id })}
    >
      <View style={styles.acquisitionHeader}>
        <View style={styles.acquisitionInfo}>
          <Text style={styles.providerName}>{getProviderName(item.providerId)}</Text>
          <Text style={styles.acquisitionDate}>{formatDate(new Date(item.date))}</Text>
          <Text style={styles.itemCount}>
            {item.items.length} item{item.items.length !== 1 ? 's' : ''}
          </Text>
        </View>
        <View style={styles.acquisitionActions}>
          <View style={styles.amountContainer}>
            <Text style={styles.acquisitionAmount}>{formatCurrency(item.totalAmount)}</Text>
            <View style={styles.statusContainer}>
              <View style={styles.status}>
                <Icon 
                  name={item.isDelivered ? 'local-shipping' : 'schedule'} 
                  size={12} 
                  color={getDeliveryStatusColor(item.isDelivered)} 
                />
                <Text style={[styles.statusText, { color: getDeliveryStatusColor(item.isDelivered) }]}>
                  {item.isDelivered ? 'Delivered' : 'Pending'}
                </Text>
              </View>
              <View style={styles.status}>
                <Icon 
                  name={item.isPaid ? 'payment' : 'money-off'} 
                  size={12} 
                  color={getPaymentStatusColor(item.isPaid)} 
                />
                <Text style={[styles.statusText, { color: getPaymentStatusColor(item.isPaid) }]}>
                  {item.isPaid ? 'Paid' : 'Unpaid'}
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('AcquisitionForm', { acquisitionId: item.id })}
            >
              <Icon name="edit" size={20} color="#FF9800" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteAcquisition(item)}
            >
              <Icon name="delete" size={20} color="#F44336" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      {/* Product Summary */}
      <View style={styles.productSummary}>
        {item.items.slice(0, 3).map((acquisitionItem, index) => (
          <Text key={index} style={styles.productItem}>
            {getProductName(acquisitionItem.productId)} (×{acquisitionItem.quantity})
          </Text>
        ))}
        {item.items.length > 3 && (
          <Text style={styles.moreItems}>+{item.items.length - 3} more items</Text>
        )}
      </View>
      
      {/* Expected Delivery */}
      {!item.isDelivered && item.expectedDeliveryDate && (
        <View style={styles.deliveryInfo}>
          <Icon name="schedule" size={14} color="#FF9800" />
          <Text style={styles.deliveryText}>
            Expected: {formatDate(new Date(item.expectedDeliveryDate))}
          </Text>
        </View>
      )}
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="inventory" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>No Acquisitions Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? 'No acquisitions match your search criteria.'
          : 'Record your first acquisition to get started.'
        }
      </Text>
    </View>
  );

  const calculateTotalAcquisitions = () => {
    return filteredAcquisitions.reduce((sum, acquisition) => sum + acquisition.totalAmount, 0);
  };

  const getPendingDeliveries = () => {
    return filteredAcquisitions.filter(acquisition => !acquisition.isDelivered).length;
  };

  const getUnpaidAcquisitions = () => {
    return filteredAcquisitions.filter(acquisition => !acquisition.isPaid).length;
  };

  if (loading) {
    return <LoadingSpinner text="Loading acquisitions..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Input
          placeholder="Search acquisitions..."
          value={searchQuery}
          onChangeText={handleSearch}
          containerStyle={styles.searchContainer}
          inputStyle={styles.searchInput}
        />
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('AcquisitionForm', {})}
        >
          <Icon name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.summaryInfo}>
        <View style={styles.resultInfo}>
          <Text style={styles.resultCount}>
            {filteredAcquisitions.length} acquisition{filteredAcquisitions.length !== 1 ? 's' : ''}
            {searchQuery && ` found for "${searchQuery}"`}
          </Text>
          <Text style={styles.summaryStats}>
            {getPendingDeliveries()} pending • {getUnpaidAcquisitions()} unpaid
          </Text>
        </View>
        <View style={styles.totalAcquisitions}>
          <Text style={styles.totalAcquisitionsText}>Total: {formatCurrency(calculateTotalAcquisitions())}</Text>
        </View>
      </View>

      <FlatList
        data={filteredAcquisitions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())}
        renderItem={renderAcquisition}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  searchContainer: {
    flex: 1,
    marginBottom: 0,
    marginRight: 12,
  },
  searchInput: {
    borderRadius: 25,
    paddingHorizontal: 16,
  },
  addButton: {
    backgroundColor: '#2196F3',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  summaryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  resultInfo: {
    flex: 1,
  },
  resultCount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  summaryStats: {
    fontSize: 12,
    color: '#999',
  },
  totalAcquisitions: {
    alignItems: 'flex-end',
  },
  totalAcquisitionsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  listContent: {
    padding: 8,
    flexGrow: 1,
  },
  acquisitionCard: {
    marginBottom: 8,
  },
  acquisitionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  acquisitionInfo: {
    flex: 1,
    marginRight: 12,
  },
  providerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  acquisitionDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  itemCount: {
    fontSize: 12,
    color: '#999',
  },
  acquisitionActions: {
    alignItems: 'flex-end',
  },
  amountContainer: {
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  acquisitionAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  statusContainer: {
    alignItems: 'flex-end',
    gap: 4,
  },
  status: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  productSummary: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
    marginBottom: 8,
  },
  productItem: {
    fontSize: 13,
    color: '#666',
    marginBottom: 2,
  },
  moreItems: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  deliveryText: {
    fontSize: 12,
    color: '#FF9800',
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default AcquisitionListScreen;