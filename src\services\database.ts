import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  Product, 
  Provider, 
  Client, 
  Acquisition, 
  Sale, 
  Debt, 
  AppSettings,
  FinanceReport 
} from '@/types';
import { mockProducts, mockProviders, mockClients, mockAcquisitions, mockSales, mockDebts, mockAppSettings } from '@/data/mockData';

const KEYS = {
  PRODUCTS: 'products',
  PROVIDERS: 'providers',
  CLIENTS: 'clients',
  ACQUISITIONS: 'acquisitions',
  SALES: 'sales',
  DEBTS: 'debts',
  APP_SETTINGS: 'appSettings',
  FIRST_LAUNCH: 'firstLaunch'
};

class DatabaseService {
  private static instance: DatabaseService;

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  async initializeDatabase(): Promise<void> {
    try {
      const firstLaunch = await AsyncStorage.getItem(KEYS.FIRST_LAUNCH);
      if (!firstLaunch) {
        // Initialize with mock data
        await this.seedDatabase();
        await AsyncStorage.setItem(KEYS.FIRST_LAUNCH, 'false');
      }
    } catch (error) {
      console.error('Error initializing database:', error);
    }
  }

  private async seedDatabase(): Promise<void> {
    await AsyncStorage.setItem(KEYS.PRODUCTS, JSON.stringify(mockProducts));
    await AsyncStorage.setItem(KEYS.PROVIDERS, JSON.stringify(mockProviders));
    await AsyncStorage.setItem(KEYS.CLIENTS, JSON.stringify(mockClients));
    await AsyncStorage.setItem(KEYS.ACQUISITIONS, JSON.stringify(mockAcquisitions));
    await AsyncStorage.setItem(KEYS.SALES, JSON.stringify(mockSales));
    await AsyncStorage.setItem(KEYS.DEBTS, JSON.stringify(mockDebts));
    await AsyncStorage.setItem(KEYS.APP_SETTINGS, JSON.stringify(mockAppSettings));
  }

  // Generic CRUD operations
  private async getAll<T>(key: string): Promise<T[]> {
    try {
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error(`Error getting ${key}:`, error);
      return [];
    }
  }

  private async saveAll<T>(key: string, items: T[]): Promise<void> {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(items));
    } catch (error) {
      console.error(`Error saving ${key}:`, error);
    }
  }

  private async getById<T extends { id: string }>(key: string, id: string): Promise<T | null> {
    const items = await this.getAll<T>(key);
    return items.find(item => item.id === id) || null;
  }

  private async create<T extends { id: string }>(key: string, item: T): Promise<T> {
    const items = await this.getAll<T>(key);
    items.push(item);
    await this.saveAll(key, items);
    return item;
  }

  private async update<T extends { id: string }>(key: string, updatedItem: T): Promise<T | null> {
    const items = await this.getAll<T>(key);
    const index = items.findIndex(item => item.id === updatedItem.id);
    if (index !== -1) {
      items[index] = updatedItem;
      await this.saveAll(key, items);
      return updatedItem;
    }
    return null;
  }

  private async delete<T extends { id: string }>(key: string, id: string): Promise<boolean> {
    const items = await this.getAll<T>(key);
    const filteredItems = items.filter(item => item.id !== id);
    if (filteredItems.length !== items.length) {
      await this.saveAll(key, filteredItems);
      return true;
    }
    return false;
  }

  // Product operations
  async getAllProducts(): Promise<Product[]> {
    return this.getAll<Product>(KEYS.PRODUCTS);
  }

  async getProductById(id: string): Promise<Product | null> {
    return this.getById<Product>(KEYS.PRODUCTS, id);
  }

  async createProduct(product: Product): Promise<Product> {
    return this.create<Product>(KEYS.PRODUCTS, product);
  }

  async updateProduct(product: Product): Promise<Product | null> {
    return this.update<Product>(KEYS.PRODUCTS, product);
  }

  async deleteProduct(id: string): Promise<boolean> {
    return this.delete<Product>(KEYS.PRODUCTS, id);
  }

  async searchProducts(query: string): Promise<Product[]> {
    const products = await this.getAllProducts();
    const lowercaseQuery = query.toLowerCase();
    return products.filter(product => 
      product.name.toLowerCase().includes(lowercaseQuery) ||
      product.manufacturer?.name?.toLowerCase().includes(lowercaseQuery) ||
      product.additionalInformation?.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Provider operations
  async getAllProviders(): Promise<Provider[]> {
    return this.getAll<Provider>(KEYS.PROVIDERS);
  }

  async getProviderById(id: string): Promise<Provider | null> {
    return this.getById<Provider>(KEYS.PROVIDERS, id);
  }

  async createProvider(provider: Provider): Promise<Provider> {
    return this.create<Provider>(KEYS.PROVIDERS, provider);
  }

  async updateProvider(provider: Provider): Promise<Provider | null> {
    return this.update<Provider>(KEYS.PROVIDERS, provider);
  }

  async deleteProvider(id: string): Promise<boolean> {
    return this.delete<Provider>(KEYS.PROVIDERS, id);
  }

  async searchProviders(query: string): Promise<Provider[]> {
    const providers = await this.getAllProviders();
    const lowercaseQuery = query.toLowerCase();
    return providers.filter(provider => 
      provider.firstName.toLowerCase().includes(lowercaseQuery) ||
      provider.lastName?.toLowerCase().includes(lowercaseQuery) ||
      provider.companyName?.toLowerCase().includes(lowercaseQuery) ||
      provider.phoneNumbers.some(phone => phone.number.includes(query))
    );
  }

  // Client operations
  async getAllClients(): Promise<Client[]> {
    return this.getAll<Client>(KEYS.CLIENTS);
  }

  async getClientById(id: string): Promise<Client | null> {
    return this.getById<Client>(KEYS.CLIENTS, id);
  }

  async createClient(client: Client): Promise<Client> {
    return this.create<Client>(KEYS.CLIENTS, client);
  }

  async updateClient(client: Client): Promise<Client | null> {
    return this.update<Client>(KEYS.CLIENTS, client);
  }

  async deleteClient(id: string): Promise<boolean> {
    return this.delete<Client>(KEYS.CLIENTS, id);
  }

  async searchClients(query: string): Promise<Client[]> {
    const clients = await this.getAllClients();
    const lowercaseQuery = query.toLowerCase();
    return clients.filter(client => 
      client.firstName.toLowerCase().includes(lowercaseQuery) ||
      client.lastName?.toLowerCase().includes(lowercaseQuery) ||
      client.phoneNumber.includes(query)
    );
  }

  // Acquisition operations
  async getAllAcquisitions(): Promise<Acquisition[]> {
    return this.getAll<Acquisition>(KEYS.ACQUISITIONS);
  }

  async getAcquisitionById(id: string): Promise<Acquisition | null> {
    return this.getById<Acquisition>(KEYS.ACQUISITIONS, id);
  }

  async createAcquisition(acquisition: Acquisition): Promise<Acquisition> {
    return this.create<Acquisition>(KEYS.ACQUISITIONS, acquisition);
  }

  async updateAcquisition(acquisition: Acquisition): Promise<Acquisition | null> {
    return this.update<Acquisition>(KEYS.ACQUISITIONS, acquisition);
  }

  async deleteAcquisition(id: string): Promise<boolean> {
    return this.delete<Acquisition>(KEYS.ACQUISITIONS, id);
  }

  // Sale operations
  async getAllSales(): Promise<Sale[]> {
    return this.getAll<Sale>(KEYS.SALES);
  }

  async getSaleById(id: string): Promise<Sale | null> {
    return this.getById<Sale>(KEYS.SALES, id);
  }

  async createSale(sale: Sale): Promise<Sale> {
    return this.create<Sale>(KEYS.SALES, sale);
  }

  async updateSale(sale: Sale): Promise<Sale | null> {
    return this.update<Sale>(KEYS.SALES, sale);
  }

  async deleteSale(id: string): Promise<boolean> {
    return this.delete<Sale>(KEYS.SALES, id);
  }

  // Debt operations
  async getAllDebts(): Promise<Debt[]> {
    return this.getAll<Debt>(KEYS.DEBTS);
  }

  async getDebtById(id: string): Promise<Debt | null> {
    return this.getById<Debt>(KEYS.DEBTS, id);
  }

  async createDebt(debt: Debt): Promise<Debt> {
    return this.create<Debt>(KEYS.DEBTS, debt);
  }

  async updateDebt(debt: Debt): Promise<Debt | null> {
    return this.update<Debt>(KEYS.DEBTS, debt);
  }

  async deleteDebt(id: string): Promise<boolean> {
    return this.delete<Debt>(KEYS.DEBTS, id);
  }

  async getOutstandingDebts(): Promise<Debt[]> {
    const debts = await this.getAllDebts();
    return debts.filter(debt => !debt.isPaid);
  }

  // App Settings operations
  async getAppSettings(): Promise<AppSettings> {
    try {
      const settings = await AsyncStorage.getItem(KEYS.APP_SETTINGS);
      return settings ? JSON.parse(settings) : mockAppSettings;
    } catch (error) {
      console.error('Error getting app settings:', error);
      return mockAppSettings;
    }
  }

  async updateAppSettings(settings: AppSettings): Promise<AppSettings> {
    try {
      await AsyncStorage.setItem(KEYS.APP_SETTINGS, JSON.stringify(settings));
      return settings;
    } catch (error) {
      console.error('Error updating app settings:', error);
      return settings;
    }
  }

  // Finance Reports
  async generateFinanceReport(startDate: Date, endDate: Date): Promise<FinanceReport> {
    const sales = await this.getAllSales();
    const acquisitions = await this.getAllAcquisitions();
    
    const filteredSales = sales.filter(sale => 
      sale.date >= startDate && sale.date <= endDate
    );
    
    const filteredAcquisitions = acquisitions.filter(acquisition => 
      acquisition.date >= startDate && acquisition.date <= endDate
    );

    const totalSalesRevenue = filteredSales.reduce((sum, sale) => sum + sale.totalAmount, 0);
    const totalAcquisitionCosts = filteredAcquisitions.reduce((sum, acquisition) => sum + acquisition.totalAmount, 0);
    
    // Sales by product
    const salesByProduct = new Map<string, { quantity: number; revenue: number }>();
    filteredSales.forEach(sale => {
      sale.items.forEach(item => {
        const existing = salesByProduct.get(item.productId) || { quantity: 0, revenue: 0 };
        salesByProduct.set(item.productId, {
          quantity: existing.quantity + item.quantity,
          revenue: existing.revenue + item.totalPrice
        });
      });
    });

    const salesByProductArray = Array.from(salesByProduct.entries()).map(([productId, data]) => ({
      productId,
      quantity: data.quantity,
      revenue: data.revenue
    }));

    // Best selling products
    const bestSellingProducts = salesByProductArray
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 10);

    return {
      startDate,
      endDate,
      totalSalesRevenue,
      totalAcquisitionCosts,
      profitLoss: totalSalesRevenue - totalAcquisitionCosts,
      salesByProduct: salesByProductArray,
      bestSellingProducts
    };
  }

  // Backup operations
  async createBackup(): Promise<string> {
    try {
      const backup = {
        products: await this.getAllProducts(),
        providers: await this.getAllProviders(),
        clients: await this.getAllClients(),
        acquisitions: await this.getAllAcquisitions(),
        sales: await this.getAllSales(),
        debts: await this.getAllDebts(),
        appSettings: await this.getAppSettings(),
        timestamp: new Date().toISOString()
      };
      
      return JSON.stringify(backup, null, 2);
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  async restoreFromBackup(backupData: string): Promise<void> {
    try {
      const backup = JSON.parse(backupData);
      
      await this.saveAll(KEYS.PRODUCTS, backup.products);
      await this.saveAll(KEYS.PROVIDERS, backup.providers);
      await this.saveAll(KEYS.CLIENTS, backup.clients);
      await this.saveAll(KEYS.ACQUISITIONS, backup.acquisitions);
      await this.saveAll(KEYS.SALES, backup.sales);
      await this.saveAll(KEYS.DEBTS, backup.debts);
      await AsyncStorage.setItem(KEYS.APP_SETTINGS, JSON.stringify(backup.appSettings));
    } catch (error) {
      console.error('Error restoring backup:', error);
      throw error;
    }
  }
}

export default DatabaseService;