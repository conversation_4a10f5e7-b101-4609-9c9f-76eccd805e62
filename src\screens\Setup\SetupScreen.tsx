import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import DatabaseService from '@/services/database';
import { AppSettings } from '@/types';
import { countries } from '@/data/countries';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';

interface SetupForm {
  userFirstName: string;
  shopName: string;
  preferredLanguage: string;
  country: string;
  currency: string;
}

const SetupScreen: React.FC = () => {
  const navigation = useNavigation();
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState<SetupForm>({
    userFirstName: '',
    shopName: '',
    preferredLanguage: 'en',
    country: 'US',
    currency: 'USD',
  });
  const [errors, setErrors] = useState<Partial<SetupForm>>({});

  const db = DatabaseService.getInstance();

  useEffect(() => {
    checkIfSetupCompleted();
  }, []);

  const checkIfSetupCompleted = async () => {
    const settings = await db.getAppSettings();
    if (!settings.isFirstUse) {
      navigation.navigate('Main' as never);
    }
  };

  const validateStep = (): boolean => {
    const newErrors: Partial<SetupForm> = {};

    switch (step) {
      case 1:
        if (!form.userFirstName.trim()) {
          newErrors.userFirstName = 'First name is required';
        }
        break;
      case 2:
        if (!form.shopName.trim()) {
          newErrors.shopName = 'Shop name is required';
        }
        break;
      case 3:
        if (!form.preferredLanguage) {
          newErrors.preferredLanguage = 'Please select a language';
        }
        break;
      case 4:
        if (!form.country) {
          newErrors.country = 'Please select a country';
        }
        break;
      case 5:
        if (!form.currency) {
          newErrors.currency = 'Please select a currency';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      if (step < 6) {
        setStep(step + 1);
      } else {
        completeSetup();
      }
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
      setErrors({});
    }
  };

  const completeSetup = async () => {
    setLoading(true);
    try {
      const settings: AppSettings = {
        userFirstName: form.userFirstName.trim(),
        shopName: form.shopName.trim(),
        preferredLanguage: form.preferredLanguage,
        country: form.country,
        currency: form.currency,
        isFirstUse: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await db.updateAppSettings(settings);
      await db.initializeDatabase();
      
      Alert.alert(
        'Setup Complete!',
        `Welcome ${form.userFirstName}! Your wholesale shop management app is ready to use.`,
        [
          {
            text: 'Get Started',
            onPress: () => navigation.navigate('Main' as never),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to complete setup. Please try again.');
      console.error('Setup error:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateForm = (field: keyof SetupForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const selectedCountry = countries.find(c => c.code === form.country);

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Welcome!</Text>
            <Text style={styles.stepDescription}>
              Let's get started by setting up your wholesale shop management app.
              First, what's your name?
            </Text>
            <Input
              label="Your First Name"
              value={form.userFirstName}
              onChangeText={(value) => updateForm('userFirstName', value)}
              error={errors.userFirstName}
              placeholder="Enter your first name"
              required
              autoCapitalize="words"
              autoFocus
            />
          </View>
        );

      case 2:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Shop Information</Text>
            <Text style={styles.stepDescription}>
              What's the name of your wholesale shop?
            </Text>
            <Input
              label="Shop Name"
              value={form.shopName}
              onChangeText={(value) => updateForm('shopName', value)}
              error={errors.shopName}
              placeholder="Enter your shop name"
              required
              autoCapitalize="words"
              autoFocus
            />
          </View>
        );

      case 3:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Language Preference</Text>
            <Text style={styles.stepDescription}>
              Choose your preferred language for the app.
            </Text>
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Preferred Language *</Text>
              <View style={styles.picker}>
                <Picker
                  selectedValue={form.preferredLanguage}
                  onValueChange={(value) => updateForm('preferredLanguage', value)}
                >
                  <Picker.Item label="English" value="en" />
                  <Picker.Item label="Spanish" value="es" />
                  <Picker.Item label="French" value="fr" />
                  <Picker.Item label="German" value="de" />
                  <Picker.Item label="Portuguese" value="pt" />
                  <Picker.Item label="Arabic" value="ar" />
                </Picker>
              </View>
              {errors.preferredLanguage && (
                <Text style={styles.errorText}>{errors.preferredLanguage}</Text>
              )}
            </View>
          </View>
        );

      case 4:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Location</Text>
            <Text style={styles.stepDescription}>
              Select your country to set regional preferences.
            </Text>
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Country *</Text>
              <View style={styles.picker}>
                <Picker
                  selectedValue={form.country}
                  onValueChange={(value) => {
                    const country = countries.find(c => c.code === value);
                    updateForm('country', value);
                    if (country) {
                      updateForm('currency', country.currency);
                    }
                  }}
                >
                  {countries.map(country => (
                    <Picker.Item
                      key={country.code}
                      label={country.name}
                      value={country.code}
                    />
                  ))}
                </Picker>
              </View>
              {errors.country && (
                <Text style={styles.errorText}>{errors.country}</Text>
              )}
            </View>
          </View>
        );

      case 5:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Currency</Text>
            <Text style={styles.stepDescription}>
              Confirm your currency for pricing and reports.
            </Text>
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Currency *</Text>
              <View style={styles.picker}>
                <Picker
                  selectedValue={form.currency}
                  onValueChange={(value) => updateForm('currency', value)}
                >
                  <Picker.Item 
                    label={`${selectedCountry?.currency || form.currency} (Recommended)`} 
                    value={selectedCountry?.currency || form.currency} 
                  />
                  <Picker.Item label="USD - US Dollar" value="USD" />
                  <Picker.Item label="EUR - Euro" value="EUR" />
                  <Picker.Item label="GBP - British Pound" value="GBP" />
                  <Picker.Item label="JPY - Japanese Yen" value="JPY" />
                  <Picker.Item label="CAD - Canadian Dollar" value="CAD" />
                  <Picker.Item label="AUD - Australian Dollar" value="AUD" />
                  <Picker.Item label="CHF - Swiss Franc" value="CHF" />
                  <Picker.Item label="CNY - Chinese Yuan" value="CNY" />
                  <Picker.Item label="INR - Indian Rupee" value="INR" />
                </Picker>
              </View>
              {errors.currency && (
                <Text style={styles.errorText}>{errors.currency}</Text>
              )}
            </View>
          </View>
        );

      case 6:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Security Notice</Text>
            <Text style={styles.stepDescription}>
              For the security of your business data, please ensure that your device 
              has proper security measures enabled (screen lock, biometrics, etc.).
            </Text>
            <Card style={styles.summaryCard}>
              <Text style={styles.summaryTitle}>Setup Summary</Text>
              <Text style={styles.summaryItem}>Name: {form.userFirstName}</Text>
              <Text style={styles.summaryItem}>Shop: {form.shopName}</Text>
              <Text style={styles.summaryItem}>Country: {selectedCountry?.name}</Text>
              <Text style={styles.summaryItem}>Currency: {form.currency}</Text>
            </Card>
            <Text style={styles.finalNote}>
              Tap "Complete Setup" to finish the configuration and start using your 
              wholesale shop management app.
            </Text>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Setup Your App</Text>
          <Text style={styles.subtitle}>Step {step} of 6</Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${(step / 6) * 100}%` }
              ]} 
            />
          </View>
        </View>

        {renderStep()}

        <View style={styles.buttonContainer}>
          {step > 1 && (
            <Button
              title="Back"
              onPress={handleBack}
              variant="secondary"
              style={styles.button}
            />
          )}
          <Button
            title={step === 6 ? "Complete Setup" : "Next"}
            onPress={handleNext}
            loading={loading}
            style={[styles.button, step === 1 && styles.fullWidthButton]}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#2196F3',
    borderRadius: 2,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  picker: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  errorText: {
    fontSize: 12,
    color: '#F44336',
    marginTop: 4,
  },
  summaryCard: {
    marginVertical: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  summaryItem: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  finalNote: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
    gap: 12,
  },
  button: {
    flex: 1,
  },
  fullWidthButton: {
    flex: 1,
  },
});

export default SetupScreen;