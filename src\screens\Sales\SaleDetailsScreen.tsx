import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  FlatList,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Sale, Client, Product, SaleStackParamList } from '@/types';
import { formatDate, formatCurrency, makePhoneCall, sendSMS } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type SaleDetailsProps = StackScreenProps<SaleStackParamList, 'SaleDetails'>;
type SaleDetailsNavigationProp = StackNavigationProp<SaleStackParamList, 'SaleDetails'>;

const SaleDetailsScreen: React.FC<SaleDetailsProps> = () => {
  const navigation = useNavigation<SaleDetailsNavigationProp>();
  const route = useRoute<SaleDetailsProps['route']>();
  const { saleId } = route.params;

  const [sale, setSale] = useState<Sale | null>(null);
  const [client, setClient] = useState<Client | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadSaleDetails();
  }, [saleId]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadSaleDetails();
    });
    return unsubscribe;
  }, [navigation]);

  const loadSaleDetails = async () => {
    try {
      setLoading(true);
      
      const [saleData, allProducts] = await Promise.all([
        db.getSaleById(saleId),
        db.getAllProducts(),
      ]);

      if (!saleData) {
        Alert.alert('Error', 'Sale not found', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
        return;
      }

      setSale(saleData);
      setProducts(allProducts);
      
      // Load client information if exists
      if (saleData.clientId) {
        const clientData = await db.getClientById(saleData.clientId);
        setClient(clientData);
      }
    } catch (error) {
      console.error('Error loading sale details:', error);
      Alert.alert('Error', 'Failed to load sale details');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('SaleForm', { saleId });
  };

  const handleDelete = () => {
    if (!sale) return;
    
    Alert.alert(
      'Delete Sale',
      `Are you sure you want to delete this sale of ${formatCurrency(sale.totalAmount)}?\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteSale(saleId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete sale');
            }
          },
        },
      ]
    );
  };

  const handleContactClient = (method: 'call' | 'sms') => {
    if (!client) return;
    
    if (method === 'call') {
      makePhoneCall(client.phoneNumber);
    } else {
      sendSMS(client.phoneNumber);
    }
  };

  const getProductName = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'Unknown Product';
  };

  const getProductUnit = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.unitOfMeasure || '';
  };

  const getReceiptStatusColor = (status: 'issued' | 'not_issued') => {
    return status === 'issued' ? '#4CAF50' : '#FF9800';
  };

  const getReceiptStatusIcon = (status: 'issued' | 'not_issued') => {
    return status === 'issued' ? 'receipt' : 'receipt-long';
  };

  const renderSaleItem = ({ item, index }: { item: any; index: number }) => (
    <Card style={styles.itemCard}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemNumber}>#{index + 1}</Text>
        <Text style={styles.itemTotal}>{formatCurrency(item.totalPrice)}</Text>
      </View>
      
      <Text style={styles.productName}>{getProductName(item.productId)}</Text>
      
      <View style={styles.itemDetails}>
        <View style={styles.itemDetailRow}>
          <Text style={styles.itemDetailLabel}>Quantity:</Text>
          <Text style={styles.itemDetailValue}>
            {item.quantity} {getProductUnit(item.productId)}
          </Text>
        </View>
        <View style={styles.itemDetailRow}>
          <Text style={styles.itemDetailLabel}>Unit Price:</Text>
          <Text style={styles.itemDetailValue}>
            {formatCurrency(item.pricePerUnit)}
          </Text>
        </View>
        <View style={styles.itemDetailRow}>
          <Text style={styles.itemDetailLabel}>Total:</Text>
          <Text style={[styles.itemDetailValue, styles.itemTotalValue]}>
            {formatCurrency(item.totalPrice)}
          </Text>
        </View>
      </View>
    </Card>
  );

  if (loading) {
    return <LoadingSpinner text="Loading sale details..." />;
  }

  if (!sale) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Sale not found</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Sale Header */}
        <Card style={styles.headerCard}>
          <View style={styles.saleHeader}>
            <View style={styles.saleInfo}>
              <Text style={styles.saleId}>Sale #{sale.id.slice(-8).toUpperCase()}</Text>
              <Text style={styles.saleDate}>{formatDate(new Date(sale.date))}</Text>
              <Text style={styles.createdDate}>
                Created {formatDate(new Date(sale.createdAt))}
              </Text>
            </View>
            <View style={styles.totalContainer}>
              <Text style={styles.totalAmount}>{formatCurrency(sale.totalAmount)}</Text>
              <View style={styles.receiptStatus}>
                <Icon 
                  name={getReceiptStatusIcon(sale.receiptStatus)} 
                  size={16} 
                  color={getReceiptStatusColor(sale.receiptStatus)} 
                />
                <Text style={[styles.receiptText, { color: getReceiptStatusColor(sale.receiptStatus) }]}>
                  {sale.receiptStatus === 'issued' ? 'Receipt Issued' : 'No Receipt'}
                </Text>
              </View>
            </View>
          </View>
        </Card>

        {/* Customer Information */}
        <Card style={styles.customerCard}>
          <Text style={styles.sectionTitle}>Customer Information</Text>
          {client ? (
            <View>
              <View style={styles.customerInfo}>
                <Text style={styles.customerName}>
                  {client.firstName} {client.lastName || ''}
                </Text>
                <Text style={styles.customerPhone}>{client.phoneNumber}</Text>
                {client.note && (
                  <Text style={styles.customerNote}>{client.note}</Text>
                )}
              </View>
              
              <View style={styles.contactActions}>
                <TouchableOpacity
                  style={[styles.contactButton, styles.callButton]}
                  onPress={() => handleContactClient('call')}
                >
                  <Icon name="phone" size={18} color="#fff" />
                  <Text style={styles.contactButtonText}>Call</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.contactButton, styles.smsButton]}
                  onPress={() => handleContactClient('sms')}
                >
                  <Icon name="sms" size={18} color="#fff" />
                  <Text style={styles.contactButtonText}>SMS</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.walkInCustomer}>
              <Icon name="person" size={24} color="#999" />
              <Text style={styles.walkInText}>Walk-in Customer</Text>
              <Text style={styles.walkInSubtext}>No customer information recorded</Text>
            </View>
          )}
        </Card>

        {/* Sale Items */}
        <Card style={styles.itemsCard}>
          <View style={styles.itemsHeader}>
            <Text style={styles.sectionTitle}>Items Sold</Text>
            <Text style={styles.itemsCount}>({sale.items.length} items)</Text>
          </View>
          
          <FlatList
            data={sale.items}
            renderItem={renderSaleItem}
            keyExtractor={(_, index) => index.toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
          
          {/* Sale Summary */}
          <View style={styles.summaryContainer}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Items:</Text>
              <Text style={styles.summaryValue}>{sale.items.length}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Quantity:</Text>
              <Text style={styles.summaryValue}>
                {sale.items.reduce((sum, item) => sum + item.quantity, 0)}
              </Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total Amount:</Text>
              <Text style={styles.totalValue}>{formatCurrency(sale.totalAmount)}</Text>
            </View>
          </View>
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          title="Edit Sale"
          onPress={handleEdit}
          style={styles.editButton}
          textStyle={styles.editButtonText}
        />
        <Button
          title="Delete"
          onPress={handleDelete}
          style={styles.deleteButton}
          textStyle={styles.deleteButtonText}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorText: {
    fontSize: 18,
    color: '#999',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
  },
  saleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  saleInfo: {
    flex: 1,
  },
  saleId: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  saleDate: {
    fontSize: 16,
    color: '#666',
    marginBottom: 2,
  },
  createdDate: {
    fontSize: 12,
    color: '#999',
  },
  totalContainer: {
    alignItems: 'flex-end',
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  receiptStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  receiptText: {
    fontSize: 12,
    fontWeight: '500',
  },
  customerCard: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  customerInfo: {
    marginBottom: 16,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  customerPhone: {
    fontSize: 14,
    color: '#2196F3',
    marginBottom: 4,
  },
  customerNote: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  contactActions: {
    flexDirection: 'row',
    gap: 12,
  },
  contactButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 6,
    gap: 6,
  },
  callButton: {
    backgroundColor: '#4CAF50',
  },
  smsButton: {
    backgroundColor: '#2196F3',
  },
  contactButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  walkInCustomer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  walkInText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#999',
    marginTop: 8,
    marginBottom: 4,
  },
  walkInSubtext: {
    fontSize: 12,
    color: '#ccc',
  },
  itemsCard: {
    marginHorizontal: 16,
    marginBottom: 80,
  },
  itemsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  itemsCount: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  itemCard: {
    marginBottom: 8,
    backgroundColor: '#fafafa',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemNumber: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
    backgroundColor: '#e0e0e0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  itemDetails: {
    gap: 4,
  },
  itemDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemDetailLabel: {
    fontSize: 14,
    color: '#666',
  },
  itemDetailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  itemTotalValue: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  summaryContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  editButton: {
    flex: 1,
    backgroundColor: '#FF9800',
  },
  editButtonText: {
    color: '#fff',
  },
  deleteButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 24,
  },
  deleteButtonText: {
    color: '#fff',
  },
});

export default SaleDetailsScreen;