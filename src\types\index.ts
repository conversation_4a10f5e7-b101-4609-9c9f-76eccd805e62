// Core entities
export interface Product {
  id: string;
  name: string;
  photo?: string;
  salableIndividually: boolean;
  wholesaleQuantity: string;
  unitOfMeasure: string;
  manufacturer?: {
    name?: string;
    country?: string;
    address?: string;
    phone?: string;
    email?: string;
    website?: string;
  };
  storageConsiderations?: string;
  additionalInformation?: string;
  providers: string[]; // Provider IDs
  createdAt: Date;
  updatedAt: Date;
}

export interface Provider {
  id: string;
  firstName: string;
  lastName?: string;
  phoneNumbers: PhoneNumber[];
  email?: string;
  photo?: string;
  website?: string;
  companyName?: string;
  address?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PhoneNumber {
  number: string;
  hasWhatsApp: boolean;
  isPrimary: boolean;
}

export interface Client {
  id: string;
  firstName: string;
  lastName?: string;
  phoneNumber: string;
  note?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AcquisitionItem {
  productId: string;
  quantity: number;
  pricePerUnit: number;
  totalPrice: number;
}

export interface Acquisition {
  id: string;
  date: Date;
  items: AcquisitionItem[];
  providerId?: string;
  isDelivered: boolean;
  expectedDeliveryDate?: Date;
  deliveryNotes?: string;
  isPaid: boolean;
  totalAmount: number;
  financeRecordId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaleItem {
  productId: string;
  quantity: number;
  pricePerUnit: number;
  totalPrice: number;
}

export interface Sale {
  id: string;
  date: Date;
  items: SaleItem[];
  clientId?: string;
  receiptStatus: 'issued' | 'not_issued';
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Debt {
  id: string;
  debtorId: string; // Client ID or separate debtor
  amount: number;
  dateOfDebt: Date;
  dueDate?: Date;
  notes?: string;
  saleId?: string;
  isPaid: boolean;
  reminderSettings: ReminderSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface ReminderSettings {
  enabled: boolean;
  customReminders: Date[];
  dailyReminderEnabled: boolean;
}

export interface FinanceReport {
  startDate: Date;
  endDate: Date;
  totalSalesRevenue: number;
  totalAcquisitionCosts: number;
  profitLoss: number;
  salesByProduct: Array<{
    productId: string;
    quantity: number;
    revenue: number;
  }>;
  bestSellingProducts: Array<{
    productId: string;
    quantitySold: number;
    revenue: number;
  }>;
}

export interface Country {
  code: string;
  name: string;
  languages: string[];
  currency: string;
}

export interface AppSettings {
  userFirstName: string;
  shopName: string;
  preferredLanguage: string;
  country: string;
  currency: string;
  isFirstUse: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Navigation types
export type RootStackParamList = {
  Setup: undefined;
  Main: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Products: undefined;
  Providers: undefined;
  Clients: undefined;
  Acquisitions: undefined;
  Sales: undefined;
  Debtors: undefined;
  Reports: undefined;
};

export type ProductStackParamList = {
  ProductList: undefined;
  ProductDetails: { productId: string };
  ProductForm: { productId?: string };
};

export type ProviderStackParamList = {
  ProviderList: undefined;
  ProviderDetails: { providerId: string };
  ProviderForm: { providerId?: string };
};

export type ClientStackParamList = {
  ClientList: undefined;
  ClientDetails: { clientId: string };
  ClientForm: { clientId?: string };
};

export type AcquisitionStackParamList = {
  AcquisitionList: undefined;
  AcquisitionDetails: { acquisitionId: string };
  AcquisitionForm: { acquisitionId?: string };
};

export type SaleStackParamList = {
  SaleList: undefined;
  SaleDetails: { saleId: string };
  SaleForm: { saleId?: string };
};

export type DebtorStackParamList = {
  DebtorList: undefined;
  DebtorDetails: { debtId: string };
  DebtorForm: { debtId?: string };
};

export type ReportStackParamList = {
  ReportDashboard: undefined;
  FinanceReport: { 
    period: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
    startDate?: Date;
    endDate?: Date;
  };
  ProductStatistics: undefined;
};