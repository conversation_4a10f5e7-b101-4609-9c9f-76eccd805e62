import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Image,
  Linking,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Product, Provider, ProductStackParamList } from '@/types';
import { openEmail, openWebsite } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ProductDetailsProps = StackScreenProps<ProductStackParamList, 'ProductDetails'>;
type ProductDetailsNavigationProp = StackNavigationProp<ProductStackParamList, 'ProductDetails'>;

const ProductDetailsScreen: React.FC<ProductDetailsProps> = () => {
  const navigation = useNavigation<ProductDetailsNavigationProp>();
  const route = useRoute<ProductDetailsProps['route']>();
  const { productId } = route.params;

  const [product, setProduct] = useState<Product | null>(null);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadProductDetails();
  }, [productId]);

  const loadProductDetails = async () => {
    try {
      setLoading(true);
      
      const [productData, allProviders] = await Promise.all([
        db.getProductById(productId),
        db.getAllProviders(),
      ]);

      if (!productData) {
        Alert.alert('Error', 'Product not found', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
        return;
      }

      setProduct(productData);
      
      // Filter providers for this product
      const productProviders = allProviders.filter(provider =>
        productData.providers.includes(provider.id)
      );
      setProviders(productProviders);
      
    } catch (error) {
      console.error('Error loading product details:', error);
      Alert.alert('Error', 'Failed to load product details');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('ProductForm', { productId });
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Product',
      `Are you sure you want to delete "${product?.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteProduct(productId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete product');
            }
          },
        },
      ]
    );
  };

  const handleContactProvider = (provider: Provider) => {
    const options = [];
    
    if (provider.phoneNumbers.length > 0) {
      options.push({ text: 'Call', onPress: () => Linking.openURL(`tel:${provider.phoneNumbers[0].number}`) });
      
      const whatsappPhone = provider.phoneNumbers.find(p => p.hasWhatsApp);
      if (whatsappPhone) {
        options.push({ 
          text: 'WhatsApp', 
          onPress: () => Linking.openURL(`whatsapp://send?phone=${whatsappPhone.number}`) 
        });
      }
    }
    
    if (provider.email) {
      options.push({ 
        text: 'Email', 
        onPress: () => openEmail(provider.email!, `Inquiry about ${product?.name}`) 
      });
    }
    
    if (provider.website) {
      options.push({ 
        text: 'Website', 
        onPress: () => openWebsite(provider.website!) 
      });
    }
    
    options.push({ text: 'Cancel', style: 'cancel' });

    Alert.alert(
      'Contact Provider',
      `Contact ${provider.firstName} ${provider.lastName || ''}`.trim(),
      options
    );
  };

  if (loading) {
    return <LoadingSpinner text="Loading product details..." />;
  }

  if (!product) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error" size={64} color="#ccc" />
          <Text style={styles.errorText}>Product not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Product Image */}
        {product.photo ? (
          <Image source={{ uri: product.photo }} style={styles.productImage} />
        ) : (
          <View style={styles.placeholderImage}>
            <Icon name="inventory" size={64} color="#ccc" />
          </View>
        )}

        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={styles.productName}>{product.name}</Text>
          
          <View style={styles.infoRow}>
            <Icon name="straighten" size={20} color="#666" />
            <Text style={styles.infoText}>
              {product.wholesaleQuantity} • {product.unitOfMeasure}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Icon name="shopping-cart" size={20} color="#666" />
            <Text style={styles.infoText}>
              {product.salableIndividually ? 'Can be sold individually' : 'Wholesale only'}
            </Text>
          </View>

          {product.additionalInformation && (
            <View style={styles.infoRow}>
              <Icon name="info" size={20} color="#666" />
              <Text style={styles.infoText}>{product.additionalInformation}</Text>
            </View>
          )}
        </Card>

        {/* Manufacturer Information */}
        {product.manufacturer && (
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Manufacturer</Text>
            
            <View style={styles.manufacturerInfo}>
              <Text style={styles.manufacturerName}>
                {product.manufacturer.name || 'Unknown Manufacturer'}
              </Text>
              
              {product.manufacturer.country && (
                <View style={styles.infoRow}>
                  <Icon name="location-on" size={20} color="#666" />
                  <Text style={styles.infoText}>{product.manufacturer.country}</Text>
                </View>
              )}
              
              {product.manufacturer.address && (
                <View style={styles.infoRow}>
                  <Icon name="home" size={20} color="#666" />
                  <Text style={styles.infoText}>{product.manufacturer.address}</Text>
                </View>
              )}
              
              {product.manufacturer.phone && (
                <TouchableOpacity
                  style={styles.infoRow}
                  onPress={() => Linking.openURL(`tel:${product.manufacturer!.phone}`)}
                >
                  <Icon name="phone" size={20} color="#2196F3" />
                  <Text style={[styles.infoText, styles.linkText]}>
                    {product.manufacturer.phone}
                  </Text>
                </TouchableOpacity>
              )}
              
              {product.manufacturer.email && (
                <TouchableOpacity
                  style={styles.infoRow}
                  onPress={() => openEmail(product.manufacturer!.email!)}
                >
                  <Icon name="email" size={20} color="#2196F3" />
                  <Text style={[styles.infoText, styles.linkText]}>
                    {product.manufacturer.email}
                  </Text>
                </TouchableOpacity>
              )}
              
              {product.manufacturer.website && (
                <TouchableOpacity
                  style={styles.infoRow}
                  onPress={() => openWebsite(product.manufacturer!.website!)}
                >
                  <Icon name="language" size={20} color="#2196F3" />
                  <Text style={[styles.infoText, styles.linkText]}>
                    {product.manufacturer.website}
                  </Text>
                </TouchableOpacity>
              )}
            </div>
          </Card>
        )}

        {/* Storage Considerations */}
        {product.storageConsiderations && (
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Storage Considerations</Text>
            <View style={styles.infoRow}>
              <Icon name="storage" size={20} color="#666" />
              <Text style={styles.infoText}>{product.storageConsiderations}</Text>
            </View>
          </Card>
        )}

        {/* Providers */}
        {providers.length > 0 && (
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Providers ({providers.length})</Text>
            {providers.map((provider) => (
              <TouchableOpacity
                key={provider.id}
                style={styles.providerCard}
                onPress={() => handleContactProvider(provider)}
              >
                <View style={styles.providerInfo}>
                  <Text style={styles.providerName}>
                    {provider.firstName} {provider.lastName || ''}
                  </Text>
                  {provider.companyName && (
                    <Text style={styles.companyName}>{provider.companyName}</Text>
                  )}
                  {provider.phoneNumbers.length > 0 && (
                    <Text style={styles.providerPhone}>
                      {provider.phoneNumbers[0].number}
                    </Text>
                  )}
                </View>
                <Icon name="chevron-right" size={24} color="#ccc" />
              </TouchableOpacity>
            ))}
          </Card>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Edit Product"
            onPress={handleEdit}
            variant="primary"
            style={styles.actionButton}
          />
          <Button
            title="Delete Product"
            onPress={handleDelete}
            variant="danger"
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  productImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  placeholderImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    margin: 16,
    marginBottom: 8,
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
    flex: 1,
    lineHeight: 24,
  },
  linkText: {
    color: '#2196F3',
    textDecorationLine: 'underline',
  },
  manufacturerInfo: {
    marginTop: 8,
  },
  manufacturerName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  providerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginBottom: 8,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  companyName: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  providerPhone: {
    fontSize: 14,
    color: '#2196F3',
    marginTop: 2,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginTop: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    color: '#999',
    marginTop: 16,
  },
});

export default ProductDetailsScreen;