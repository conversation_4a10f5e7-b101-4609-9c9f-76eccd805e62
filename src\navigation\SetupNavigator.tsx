import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import SetupScreen from '@/screens/Setup/SetupScreen';

const Stack = createStackNavigator();

const SetupNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="SetupScreen" component={SetupScreen} />
    </Stack.Navigator>
  );
};

export default SetupNavigator;