import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { MainTabParamList, ProductStackParamList, ProviderStackParamList, ClientStackParamList, AcquisitionStackParamList, SaleStackParamList, DebtorStackParamList, ReportStackParamList } from '@/types';

// Import screens (we'll create these next)
import DashboardScreen from '@/screens/Dashboard/DashboardScreen';
import ProductListScreen from '@/screens/Products/ProductListScreen';
import ProductDetailsScreen from '@/screens/Products/ProductDetailsScreen';
import ProductFormScreen from '@/screens/Products/ProductFormScreen';
import ProviderListScreen from '@/screens/Providers/ProviderListScreen';
import ProviderDetailsScreen from '@/screens/Providers/ProviderDetailsScreen';
import ProviderFormScreen from '@/screens/Providers/ProviderFormScreen';
import ClientListScreen from '@/screens/Clients/ClientListScreen';
import ClientDetailsScreen from '@/screens/Clients/ClientDetailsScreen';
import ClientFormScreen from '@/screens/Clients/ClientFormScreen';
import AcquisitionListScreen from '@/screens/Acquisitions/AcquisitionListScreen';
import AcquisitionDetailsScreen from '@/screens/Acquisitions/AcquisitionDetailsScreen';
import AcquisitionFormScreen from '@/screens/Acquisitions/AcquisitionFormScreen';
import SaleListScreen from '@/screens/Sales/SaleListScreen';
import SaleDetailsScreen from '@/screens/Sales/SaleDetailsScreen';
import SaleFormScreen from '@/screens/Sales/SaleFormScreen';
import DebtorListScreen from '@/screens/Debtors/DebtorListScreen';
import DebtorDetailsScreen from '@/screens/Debtors/DebtorDetailsScreen';
import DebtorFormScreen from '@/screens/Debtors/DebtorFormScreen';
import ReportDashboardScreen from '@/screens/Reports/ReportDashboardScreen';
import FinanceReportScreen from '@/screens/Reports/FinanceReportScreen';
import ProductStatisticsScreen from '@/screens/Reports/ProductStatisticsScreen';

const Tab = createBottomTabNavigator<MainTabParamList>();
const ProductStack = createStackNavigator<ProductStackParamList>();
const ProviderStack = createStackNavigator<ProviderStackParamList>();
const ClientStack = createStackNavigator<ClientStackParamList>();
const AcquisitionStack = createStackNavigator<AcquisitionStackParamList>();
const SaleStack = createStackNavigator<SaleStackParamList>();
const DebtorStack = createStackNavigator<DebtorStackParamList>();
const ReportStack = createStackNavigator<ReportStackParamList>();

// Stack Navigators
const ProductNavigator = () => (
  <ProductStack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: '#2196F3' },
      headerTintColor: '#fff',
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <ProductStack.Screen 
      name="ProductList" 
      component={ProductListScreen}
      options={{ title: 'Products' }}
    />
    <ProductStack.Screen 
      name="ProductDetails" 
      component={ProductDetailsScreen}
      options={{ title: 'Product Details' }}
    />
    <ProductStack.Screen 
      name="ProductForm" 
      component={ProductFormScreen}
      options={({ route }) => ({ 
        title: route.params?.productId ? 'Edit Product' : 'Add Product' 
      })}
    />
  </ProductStack.Navigator>
);

const ProviderNavigator = () => (
  <ProviderStack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: '#4CAF50' },
      headerTintColor: '#fff',
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <ProviderStack.Screen 
      name="ProviderList" 
      component={ProviderListScreen}
      options={{ title: 'Providers' }}
    />
    <ProviderStack.Screen 
      name="ProviderDetails" 
      component={ProviderDetailsScreen}
      options={{ title: 'Provider Details' }}
    />
    <ProviderStack.Screen 
      name="ProviderForm" 
      component={ProviderFormScreen}
      options={({ route }) => ({ 
        title: route.params?.providerId ? 'Edit Provider' : 'Add Provider' 
      })}
    />
  </ProviderStack.Navigator>
);

const ClientNavigator = () => (
  <ClientStack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: '#FF9800' },
      headerTintColor: '#fff',
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <ClientStack.Screen 
      name="ClientList" 
      component={ClientListScreen}
      options={{ title: 'Clients' }}
    />
    <ClientStack.Screen 
      name="ClientDetails" 
      component={ClientDetailsScreen}
      options={{ title: 'Client Details' }}
    />
    <ClientStack.Screen 
      name="ClientForm" 
      component={ClientFormScreen}
      options={({ route }) => ({ 
        title: route.params?.clientId ? 'Edit Client' : 'Add Client' 
      })}
    />
  </ClientStack.Navigator>
);

const AcquisitionNavigator = () => (
  <AcquisitionStack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: '#9C27B0' },
      headerTintColor: '#fff',
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <AcquisitionStack.Screen 
      name="AcquisitionList" 
      component={AcquisitionListScreen}
      options={{ title: 'Acquisitions' }}
    />
    <AcquisitionStack.Screen 
      name="AcquisitionDetails" 
      component={AcquisitionDetailsScreen}
      options={{ title: 'Acquisition Details' }}
    />
    <AcquisitionStack.Screen 
      name="AcquisitionForm" 
      component={AcquisitionFormScreen}
      options={({ route }) => ({ 
        title: route.params?.acquisitionId ? 'Edit Acquisition' : 'Add Acquisition' 
      })}
    />
  </AcquisitionStack.Navigator>
);

const SaleNavigator = () => (
  <SaleStack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: '#00BCD4' },
      headerTintColor: '#fff',
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <SaleStack.Screen 
      name="SaleList" 
      component={SaleListScreen}
      options={{ title: 'Sales' }}
    />
    <SaleStack.Screen 
      name="SaleDetails" 
      component={SaleDetailsScreen}
      options={{ title: 'Sale Details' }}
    />
    <SaleStack.Screen 
      name="SaleForm" 
      component={SaleFormScreen}
      options={({ route }) => ({ 
        title: route.params?.saleId ? 'Edit Sale' : 'Add Sale' 
      })}
    />
  </SaleStack.Navigator>
);

const DebtorNavigator = () => (
  <DebtorStack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: '#F44336' },
      headerTintColor: '#fff',
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <DebtorStack.Screen 
      name="DebtorList" 
      component={DebtorListScreen}
      options={{ title: 'Debtors' }}
    />
    <DebtorStack.Screen 
      name="DebtorDetails" 
      component={DebtorDetailsScreen}
      options={{ title: 'Debt Details' }}
    />
    <DebtorStack.Screen 
      name="DebtorForm" 
      component={DebtorFormScreen}
      options={({ route }) => ({ 
        title: route.params?.debtId ? 'Edit Debt' : 'Add Debt' 
      })}
    />
  </DebtorStack.Navigator>
);

const ReportNavigator = () => (
  <ReportStack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: '#607D8B' },
      headerTintColor: '#fff',
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <ReportStack.Screen 
      name="ReportDashboard" 
      component={ReportDashboardScreen}
      options={{ title: 'Reports' }}
    />
    <ReportStack.Screen 
      name="FinanceReport" 
      component={FinanceReportScreen}
      options={{ title: 'Finance Report' }}
    />
    <ReportStack.Screen 
      name="ProductStatistics" 
      component={ProductStatisticsScreen}
      options={{ title: 'Product Statistics' }}
    />
  </ReportStack.Navigator>
);

// Tab Navigator
const MainNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Products':
              iconName = 'inventory';
              break;
            case 'Providers':
              iconName = 'business';
              break;
            case 'Clients':
              iconName = 'people';
              break;
            case 'Acquisitions':
              iconName = 'shopping-cart';
              break;
            case 'Sales':
              iconName = 'point-of-sale';
              break;
            case 'Debtors':
              iconName = 'account-balance-wallet';
              break;
            case 'Reports':
              iconName = 'analytics';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#2196F3',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarStyle: {
          backgroundColor: '#fff',
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
          height: 60,
          paddingBottom: 5,
          paddingTop: 5,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardScreen} />
      <Tab.Screen name="Products" component={ProductNavigator} />
      <Tab.Screen name="Providers" component={ProviderNavigator} />
      <Tab.Screen name="Clients" component={ClientNavigator} />
      <Tab.Screen name="Acquisitions" component={AcquisitionNavigator} />
      <Tab.Screen name="Sales" component={SaleNavigator} />
      <Tab.Screen name="Debtors" component={DebtorNavigator} />
      <Tab.Screen name="Reports" component={ReportNavigator} />
    </Tab.Navigator>
  );
};

export default MainNavigator;