import { Country } from '@/types';

export const countries: Country[] = [
  {
    code: 'US',
    name: 'United States',
    languages: ['English'],
    currency: 'USD'
  },
  {
    code: 'CA',
    name: 'Canada',
    languages: ['English', 'French'],
    currency: 'CAD'
  },
  {
    code: 'GB',
    name: 'United Kingdom',
    languages: ['English'],
    currency: 'GBP'
  },
  {
    code: 'FR',
    name: 'France',
    languages: ['French'],
    currency: 'EUR'
  },
  {
    code: 'DE',
    name: 'Germany',
    languages: ['German'],
    currency: 'EUR'
  },
  {
    code: 'ES',
    name: 'Spain',
    languages: ['Spanish'],
    currency: 'EUR'
  },
  {
    code: 'IT',
    name: 'Italy',
    languages: ['Italian'],
    currency: 'EUR'
  },
  {
    code: 'NL',
    name: 'Netherlands',
    languages: ['Dutch'],
    currency: 'EUR'
  },
  {
    code: 'BE',
    name: 'Belgium',
    languages: ['Dutch', 'French', 'German'],
    currency: 'EUR'
  },
  {
    code: 'CH',
    name: 'Switzerland',
    languages: ['German', 'French', 'Italian'],
    currency: 'CHF'
  },
  {
    code: 'AT',
    name: 'Austria',
    languages: ['German'],
    currency: 'EUR'
  },
  {
    code: 'PT',
    name: 'Portugal',
    languages: ['Portuguese'],
    currency: 'EUR'
  },
  {
    code: 'SE',
    name: 'Sweden',
    languages: ['Swedish'],
    currency: 'SEK'
  },
  {
    code: 'NO',
    name: 'Norway',
    languages: ['Norwegian'],
    currency: 'NOK'
  },
  {
    code: 'DK',
    name: 'Denmark',
    languages: ['Danish'],
    currency: 'DKK'
  },
  {
    code: 'FI',
    name: 'Finland',
    languages: ['Finnish', 'Swedish'],
    currency: 'EUR'
  },
  {
    code: 'IS',
    name: 'Iceland',
    languages: ['Icelandic'],
    currency: 'ISK'
  },
  {
    code: 'IE',
    name: 'Ireland',
    languages: ['English', 'Irish'],
    currency: 'EUR'
  },
  {
    code: 'LU',
    name: 'Luxembourg',
    languages: ['Luxembourgish', 'French', 'German'],
    currency: 'EUR'
  },
  {
    code: 'MT',
    name: 'Malta',
    languages: ['Maltese', 'English'],
    currency: 'EUR'
  },
  {
    code: 'CY',
    name: 'Cyprus',
    languages: ['Greek', 'Turkish'],
    currency: 'EUR'
  },
  {
    code: 'GR',
    name: 'Greece',
    languages: ['Greek'],
    currency: 'EUR'
  },
  {
    code: 'BG',
    name: 'Bulgaria',
    languages: ['Bulgarian'],
    currency: 'BGN'
  },
  {
    code: 'RO',
    name: 'Romania',
    languages: ['Romanian'],
    currency: 'RON'
  },
  {
    code: 'HR',
    name: 'Croatia',
    languages: ['Croatian'],
    currency: 'EUR'
  },
  {
    code: 'SI',
    name: 'Slovenia',
    languages: ['Slovenian'],
    currency: 'EUR'
  },
  {
    code: 'SK',
    name: 'Slovakia',
    languages: ['Slovak'],
    currency: 'EUR'
  },
  {
    code: 'CZ',
    name: 'Czech Republic',
    languages: ['Czech'],
    currency: 'CZK'
  },
  {
    code: 'HU',
    name: 'Hungary',
    languages: ['Hungarian'],
    currency: 'HUF'
  },
  {
    code: 'PL',
    name: 'Poland',
    languages: ['Polish'],
    currency: 'PLN'
  },
  {
    code: 'LT',
    name: 'Lithuania',
    languages: ['Lithuanian'],
    currency: 'EUR'
  },
  {
    code: 'LV',
    name: 'Latvia',
    languages: ['Latvian'],
    currency: 'EUR'
  },
  {
    code: 'EE',
    name: 'Estonia',
    languages: ['Estonian'],
    currency: 'EUR'
  },
  {
    code: 'JP',
    name: 'Japan',
    languages: ['Japanese'],
    currency: 'JPY'
  },
  {
    code: 'CN',
    name: 'China',
    languages: ['Mandarin'],
    currency: 'CNY'
  },
  {
    code: 'KR',
    name: 'South Korea',
    languages: ['Korean'],
    currency: 'KRW'
  },
  {
    code: 'IN',
    name: 'India',
    languages: ['Hindi', 'English'],
    currency: 'INR'
  },
  {
    code: 'AU',
    name: 'Australia',
    languages: ['English'],
    currency: 'AUD'
  },
  {
    code: 'NZ',
    name: 'New Zealand',
    languages: ['English', 'Māori'],
    currency: 'NZD'
  },
  {
    code: 'BR',
    name: 'Brazil',
    languages: ['Portuguese'],
    currency: 'BRL'
  },
  {
    code: 'MX',
    name: 'Mexico',
    languages: ['Spanish'],
    currency: 'MXN'
  },
  {
    code: 'AR',
    name: 'Argentina',
    languages: ['Spanish'],
    currency: 'ARS'
  },
  {
    code: 'CL',
    name: 'Chile',
    languages: ['Spanish'],
    currency: 'CLP'
  },
  {
    code: 'CO',
    name: 'Colombia',
    languages: ['Spanish'],
    currency: 'COP'
  },
  {
    code: 'PE',
    name: 'Peru',
    languages: ['Spanish', 'Quechua'],
    currency: 'PEN'
  },
  {
    code: 'VE',
    name: 'Venezuela',
    languages: ['Spanish'],
    currency: 'VES'
  },
  {
    code: 'EC',
    name: 'Ecuador',
    languages: ['Spanish'],
    currency: 'USD'
  },
  {
    code: 'BO',
    name: 'Bolivia',
    languages: ['Spanish', 'Quechua', 'Aymara'],
    currency: 'BOB'
  },
  {
    code: 'PY',
    name: 'Paraguay',
    languages: ['Spanish', 'Guaraní'],
    currency: 'PYG'
  },
  {
    code: 'UY',
    name: 'Uruguay',
    languages: ['Spanish'],
    currency: 'UYU'
  },
  {
    code: 'GY',
    name: 'Guyana',
    languages: ['English'],
    currency: 'GYD'
  },
  {
    code: 'SR',
    name: 'Suriname',
    languages: ['Dutch'],
    currency: 'SRD'
  },
  {
    code: 'ZA',
    name: 'South Africa',
    languages: ['Afrikaans', 'English', 'Zulu', 'Xhosa'],
    currency: 'ZAR'
  },
  {
    code: 'EG',
    name: 'Egypt',
    languages: ['Arabic'],
    currency: 'EGP'
  },
  {
    code: 'MA',
    name: 'Morocco',
    languages: ['Arabic', 'Berber'],
    currency: 'MAD'
  },
  {
    code: 'DZ',
    name: 'Algeria',
    languages: ['Arabic', 'Berber'],
    currency: 'DZD'
  },
  {
    code: 'TN',
    name: 'Tunisia',
    languages: ['Arabic'],
    currency: 'TND'
  },
  {
    code: 'LY',
    name: 'Libya',
    languages: ['Arabic'],
    currency: 'LYD'
  },
  {
    code: 'SD',
    name: 'Sudan',
    languages: ['Arabic', 'English'],
    currency: 'SDG'
  },
  {
    code: 'ET',
    name: 'Ethiopia',
    languages: ['Amharic'],
    currency: 'ETB'
  },
  {
    code: 'KE',
    name: 'Kenya',
    languages: ['Swahili', 'English'],
    currency: 'KES'
  },
  {
    code: 'TZ',
    name: 'Tanzania',
    languages: ['Swahili', 'English'],
    currency: 'TZS'
  },
  {
    code: 'UG',
    name: 'Uganda',
    languages: ['English', 'Swahili'],
    currency: 'UGX'
  },
  {
    code: 'RW',
    name: 'Rwanda',
    languages: ['Kinyarwanda', 'French', 'English'],
    currency: 'RWF'
  },
  {
    code: 'BI',
    name: 'Burundi',
    languages: ['Kirundi', 'French'],
    currency: 'BIF'
  },
  {
    code: 'DJ',
    name: 'Djibouti',
    languages: ['French', 'Arabic'],
    currency: 'DJF'
  },
  {
    code: 'SO',
    name: 'Somalia',
    languages: ['Somali', 'Arabic'],
    currency: 'SOS'
  },
  {
    code: 'ER',
    name: 'Eritrea',
    languages: ['Tigrinya', 'Arabic', 'English'],
    currency: 'ERN'
  },
  {
    code: 'SS',
    name: 'South Sudan',
    languages: ['English'],
    currency: 'SSP'
  },
  {
    code: 'CF',
    name: 'Central African Republic',
    languages: ['French', 'Sango'],
    currency: 'XAF'
  },
  {
    code: 'TD',
    name: 'Chad',
    languages: ['French', 'Arabic'],
    currency: 'XAF'
  },
  {
    code: 'CM',
    name: 'Cameroon',
    languages: ['French', 'English'],
    currency: 'XAF'
  },
  {
    code: 'GQ',
    name: 'Equatorial Guinea',
    languages: ['Spanish', 'French', 'Portuguese'],
    currency: 'XAF'
  },
  {
    code: 'GA',
    name: 'Gabon',
    languages: ['French'],
    currency: 'XAF'
  },
  {
    code: 'CG',
    name: 'Republic of the Congo',
    languages: ['French'],
    currency: 'XAF'
  },
  {
    code: 'CD',
    name: 'Democratic Republic of the Congo',
    languages: ['French'],
    currency: 'CDF'
  },
  {
    code: 'AO',
    name: 'Angola',
    languages: ['Portuguese'],
    currency: 'AOA'
  },
  {
    code: 'ZM',
    name: 'Zambia',
    languages: ['English'],
    currency: 'ZMW'
  },
  {
    code: 'ZW',
    name: 'Zimbabwe',
    languages: ['English', 'Shona', 'Ndebele'],
    currency: 'ZWL'
  },
  {
    code: 'BW',
    name: 'Botswana',
    languages: ['English', 'Setswana'],
    currency: 'BWP'
  },
  {
    code: 'NA',
    name: 'Namibia',
    languages: ['English'],
    currency: 'NAD'
  },
  {
    code: 'SZ',
    name: 'Eswatini',
    languages: ['English', 'Swati'],
    currency: 'SZL'
  },
  {
    code: 'LS',
    name: 'Lesotho',
    languages: ['English', 'Sesotho'],
    currency: 'LSL'
  },
  {
    code: 'MW',
    name: 'Malawi',
    languages: ['English', 'Chichewa'],
    currency: 'MWK'
  },
  {
    code: 'MZ',
    name: 'Mozambique',
    languages: ['Portuguese'],
    currency: 'MZN'
  },
  {
    code: 'MG',
    name: 'Madagascar',
    languages: ['Malagasy', 'French'],
    currency: 'MGA'
  },
  {
    code: 'MU',
    name: 'Mauritius',
    languages: ['English', 'French'],
    currency: 'MUR'
  },
  {
    code: 'SC',
    name: 'Seychelles',
    languages: ['English', 'French', 'Seychellois Creole'],
    currency: 'SCR'
  },
  {
    code: 'KM',
    name: 'Comoros',
    languages: ['Comorian', 'Arabic', 'French'],
    currency: 'KMF'
  },
  {
    code: 'CV',
    name: 'Cape Verde',
    languages: ['Portuguese'],
    currency: 'CVE'
  },
  {
    code: 'ST',
    name: 'São Tomé and Príncipe',
    languages: ['Portuguese'],
    currency: 'STN'
  },
  {
    code: 'GH',
    name: 'Ghana',
    languages: ['English'],
    currency: 'GHS'
  },
  {
    code: 'TG',
    name: 'Togo',
    languages: ['French'],
    currency: 'XOF'
  },
  {
    code: 'BJ',
    name: 'Benin',
    languages: ['French'],
    currency: 'XOF'
  },
  {
    code: 'NE',
    name: 'Niger',
    languages: ['French'],
    currency: 'XOF'
  },
  {
    code: 'BF',
    name: 'Burkina Faso',
    languages: ['French'],
    currency: 'XOF'
  },
  {
    code: 'ML',
    name: 'Mali',
    languages: ['French'],
    currency: 'XOF'
  },
  {
    code: 'CI',
    name: 'Côte d\'Ivoire',
    languages: ['French'],
    currency: 'XOF'
  },
  {
    code: 'LR',
    name: 'Liberia',
    languages: ['English'],
    currency: 'LRD'
  },
  {
    code: 'SL',
    name: 'Sierra Leone',
    languages: ['English'],
    currency: 'SLE'
  },
  {
    code: 'GN',
    name: 'Guinea',
    languages: ['French'],
    currency: 'GNF'
  },
  {
    code: 'GW',
    name: 'Guinea-Bissau',
    languages: ['Portuguese'],
    currency: 'XOF'
  },
  {
    code: 'SN',
    name: 'Senegal',
    languages: ['French'],
    currency: 'XOF'
  },
  {
    code: 'GM',
    name: 'Gambia',
    languages: ['English'],
    currency: 'GMD'
  },
  {
    code: 'MR',
    name: 'Mauritania',
    languages: ['Arabic'],
    currency: 'MRU'
  },
  {
    code: 'NG',
    name: 'Nigeria',
    languages: ['English'],
    currency: 'NGN'
  },
  {
    code: 'TR',
    name: 'Turkey',
    languages: ['Turkish'],
    currency: 'TRY'
  },
  {
    code: 'IR',
    name: 'Iran',
    languages: ['Persian'],
    currency: 'IRR'
  },
  {
    code: 'IQ',
    name: 'Iraq',
    languages: ['Arabic', 'Kurdish'],
    currency: 'IQD'
  },
  {
    code: 'SY',
    name: 'Syria',
    languages: ['Arabic'],
    currency: 'SYP'
  },
  {
    code: 'LB',
    name: 'Lebanon',
    languages: ['Arabic', 'French'],
    currency: 'LBP'
  },
  {
    code: 'JO',
    name: 'Jordan',
    languages: ['Arabic'],
    currency: 'JOD'
  },
  {
    code: 'IL',
    name: 'Israel',
    languages: ['Hebrew', 'Arabic'],
    currency: 'ILS'
  },
  {
    code: 'PS',
    name: 'Palestine',
    languages: ['Arabic'],
    currency: 'ILS'
  },
  {
    code: 'SA',
    name: 'Saudi Arabia',
    languages: ['Arabic'],
    currency: 'SAR'
  },
  {
    code: 'YE',
    name: 'Yemen',
    languages: ['Arabic'],
    currency: 'YER'
  },
  {
    code: 'OM',
    name: 'Oman',
    languages: ['Arabic'],
    currency: 'OMR'
  },
  {
    code: 'AE',
    name: 'United Arab Emirates',
    languages: ['Arabic'],
    currency: 'AED'
  },
  {
    code: 'QA',
    name: 'Qatar',
    languages: ['Arabic'],
    currency: 'QAR'
  },
  {
    code: 'BH',
    name: 'Bahrain',
    languages: ['Arabic'],
    currency: 'BHD'
  },
  {
    code: 'KW',
    name: 'Kuwait',
    languages: ['Arabic'],
    currency: 'KWD'
  },
  {
    code: 'AF',
    name: 'Afghanistan',
    languages: ['Pashto', 'Dari'],
    currency: 'AFN'
  },
  {
    code: 'PK',
    name: 'Pakistan',
    languages: ['Urdu', 'English'],
    currency: 'PKR'
  },
  {
    code: 'BD',
    name: 'Bangladesh',
    languages: ['Bengali'],
    currency: 'BDT'
  },
  {
    code: 'LK',
    name: 'Sri Lanka',
    languages: ['Sinhala', 'Tamil'],
    currency: 'LKR'
  },
  {
    code: 'MV',
    name: 'Maldives',
    languages: ['Dhivehi'],
    currency: 'MVR'
  },
  {
    code: 'NP',
    name: 'Nepal',
    languages: ['Nepali'],
    currency: 'NPR'
  },
  {
    code: 'BT',
    name: 'Bhutan',
    languages: ['Dzongkha'],
    currency: 'BTN'
  },
  {
    code: 'MM',
    name: 'Myanmar',
    languages: ['Burmese'],
    currency: 'MMK'
  },
  {
    code: 'TH',
    name: 'Thailand',
    languages: ['Thai'],
    currency: 'THB'
  },
  {
    code: 'LA',
    name: 'Laos',
    languages: ['Lao'],
    currency: 'LAK'
  },
  {
    code: 'VN',
    name: 'Vietnam',
    languages: ['Vietnamese'],
    currency: 'VND'
  },
  {
    code: 'KH',
    name: 'Cambodia',
    languages: ['Khmer'],
    currency: 'KHR'
  },
  {
    code: 'MY',
    name: 'Malaysia',
    languages: ['Malay'],
    currency: 'MYR'
  },
  {
    code: 'SG',
    name: 'Singapore',
    languages: ['English', 'Malay', 'Mandarin', 'Tamil'],
    currency: 'SGD'
  },
  {
    code: 'BN',
    name: 'Brunei',
    languages: ['Malay'],
    currency: 'BND'
  },
  {
    code: 'ID',
    name: 'Indonesia',
    languages: ['Indonesian'],
    currency: 'IDR'
  },
  {
    code: 'TL',
    name: 'East Timor',
    languages: ['Tetum', 'Portuguese'],
    currency: 'USD'
  },
  {
    code: 'PH',
    name: 'Philippines',
    languages: ['Filipino', 'English'],
    currency: 'PHP'
  },
  {
    code: 'MN',
    name: 'Mongolia',
    languages: ['Mongolian'],
    currency: 'MNT'
  },
  {
    code: 'KZ',
    name: 'Kazakhstan',
    languages: ['Kazakh', 'Russian'],
    currency: 'KZT'
  },
  {
    code: 'KG',
    name: 'Kyrgyzstan',
    languages: ['Kyrgyz', 'Russian'],
    currency: 'KGS'
  },
  {
    code: 'TJ',
    name: 'Tajikistan',
    languages: ['Tajik'],
    currency: 'TJS'
  },
  {
    code: 'UZ',
    name: 'Uzbekistan',
    languages: ['Uzbek'],
    currency: 'UZS'
  },
  {
    code: 'TM',
    name: 'Turkmenistan',
    languages: ['Turkmen'],
    currency: 'TMT'
  },
  {
    code: 'RU',
    name: 'Russia',
    languages: ['Russian'],
    currency: 'RUB'
  },
  {
    code: 'GE',
    name: 'Georgia',
    languages: ['Georgian'],
    currency: 'GEL'
  },
  {
    code: 'AM',
    name: 'Armenia',
    languages: ['Armenian'],
    currency: 'AMD'
  },
  {
    code: 'AZ',
    name: 'Azerbaijan',
    languages: ['Azerbaijani'],
    currency: 'AZN'
  },
  {
    code: 'BY',
    name: 'Belarus',
    languages: ['Belarusian', 'Russian'],
    currency: 'BYN'
  },
  {
    code: 'UA',
    name: 'Ukraine',
    languages: ['Ukrainian'],
    currency: 'UAH'
  },
  {
    code: 'MD',
    name: 'Moldova',
    languages: ['Romanian'],
    currency: 'MDL'
  },
  {
    code: 'RS',
    name: 'Serbia',
    languages: ['Serbian'],
    currency: 'RSD'
  },
  {
    code: 'ME',
    name: 'Montenegro',
    languages: ['Montenegrin'],
    currency: 'EUR'
  },
  {
    code: 'BA',
    name: 'Bosnia and Herzegovina',
    languages: ['Bosnian', 'Croatian', 'Serbian'],
    currency: 'BAM'
  },
  {
    code: 'XK',
    name: 'Kosovo',
    languages: ['Albanian', 'Serbian'],
    currency: 'EUR'
  },
  {
    code: 'AL',
    name: 'Albania',
    languages: ['Albanian'],
    currency: 'ALL'
  },
  {
    code: 'MK',
    name: 'North Macedonia',
    languages: ['Macedonian'],
    currency: 'MKD'
  }
];