# Wholesale Shop Management Application

A comprehensive offline React Native application designed for wholesale shop owners to efficiently manage their business operations including products, suppliers, customers, sales, acquisitions, and financial reporting.

## 📱 Overview

The Wholesale Shop Management Application provides a streamlined, mobile-first solution for wholesale businesses to:

- Manage product inventory and supplier relationships
- Track sales and acquisitions with detailed reporting
- Monitor customer debts and send payment reminders
- Generate comprehensive financial reports and analytics
- Maintain all data offline for reliable access

## 🚀 Features

### Core Management Modules
- **Product Management**: Comprehensive product catalog with photos, specifications, and supplier relationships
- **Provider Management**: Complete supplier contact management with direct communication capabilities
- **Client Management**: Customer database with purchase history and contact management
- **Sales Tracking**: Detailed sales recording with multi-item support and receipt management
- **Acquisition Management**: Inventory tracking with delivery status and payment monitoring
- **Debt Management**: Customer debt tracking with automated reminders and payment monitoring

### Reporting & Analytics
- **Financial Reports**: Detailed profit/loss analysis with customizable date ranges
- **Product Statistics**: Performance analytics with sales trends and inventory insights
- **Dashboard Analytics**: Real-time business overview with key performance indicators

### Key Features
- **100% Offline Operation**: No internet required for core functionality
- **Automated Reminders**: Daily debt reminder notifications at 5 PM
- **Export Capabilities**: Share reports and backup data
- **Multi-Contact Support**: Call, SMS, WhatsApp integration
- **Search & Filter**: Advanced search across all modules
- **Data Validation**: Comprehensive form validation and error handling

## 🛠 Technology Stack

- **Framework**: React Native with TypeScript
- **Navigation**: React Navigation 6
- **Storage**: AsyncStorage for local data persistence
- **UI Components**: Custom component library with Material Design icons
- **Date Handling**: React Native Community DateTimePicker
- **Utilities**: Vector Icons, Phone number formatting, Currency formatting

## 📁 Project Structure

```
src/
├── components/
│   └── ui/                 # Reusable UI components
├── navigation/             # Navigation configuration
├── screens/               # Application screens
│   ├── Acquisitions/      # Inventory acquisition management
│   ├── Clients/          # Customer management
│   ├── Dashboard/        # Main dashboard
│   ├── Debtors/         # Debt management
│   ├── Products/        # Product management
│   ├── Providers/       # Supplier management
│   ├── Reports/         # Analytics and reporting
│   ├── Sales/           # Sales management
│   └── Setup/           # Initial app setup
├── services/            # Business logic and data services
├── types/               # TypeScript type definitions
├── utils/               # Helper functions and utilities
└── data/                # Mock data and initial setup
```

## 🔧 Development Setup

### Prerequisites
- Node.js (v18 or higher)
- React Native development environment
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd wholesale-shop-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **iOS Setup** (macOS only)
   ```bash
   cd ios && pod install && cd ..
   ```

4. **Start Metro bundler**
   ```bash
   npm start
   # or
   yarn start
   ```

5. **Run on device/simulator**
   ```bash
   # Android
   npm run android
   # or
   yarn android

   # iOS
   npm run ios
   # or
   yarn ios
   ```

## 📊 Database Schema

The application uses a local AsyncStorage-based database with the following main entities:

- **Products**: Product catalog with specifications and supplier relationships
- **Providers**: Supplier contact information and business details
- **Clients**: Customer database with contact information
- **Sales**: Sales transactions with itemized details
- **Acquisitions**: Inventory purchases with delivery tracking
- **Debts**: Customer debt records with payment tracking
- **AppSettings**: Application configuration and user preferences

## 🎯 Core Workflows

### Product Management
1. Add/Edit products with detailed specifications
2. Link products to suppliers
3. Set wholesale quantities and units of measure
4. Track manufacturer information

### Sales Process
1. Create new sale with customer selection
2. Add multiple products with quantities and prices
3. Generate receipt and track payment status
4. Automatically create debt records for unpaid sales

### Acquisition Workflow
1. Record incoming inventory from suppliers
2. Track delivery status and expected dates
3. Monitor payment status to suppliers
4. Update product availability

### Debt Management
1. Automatic debt creation from unpaid sales
2. Manual debt entry for additional charges
3. Set due dates and payment reminders
4. Track payment history and outstanding balances

## 📈 Reporting Features

### Financial Reports
- Revenue and cost analysis by period
- Profit margin calculations
- Top-performing products by revenue
- Daily sales breakdown with trends

### Product Analytics
- Sales performance by product
- Inventory turnover analysis
- Profit margin by product
- Stock level monitoring with low-stock alerts

### Dashboard Metrics
- Real-time business overview
- Key performance indicators
- Recent sales summary
- Outstanding debt notifications

## 🔒 Data Security & Backup

- All data stored locally on device
- Automatic daily backups
- Manual export functionality
- No external data transmission
- Secure local storage implementation

## 🎨 User Interface Design

### Design Principles
- **Mobile-First**: Optimized for mobile device usage
- **One Task Per Screen**: Clear, focused user experience
- **Consistent Navigation**: Intuitive navigation patterns
- **Accessibility**: Screen reader compatible with proper labeling
- **Performance**: Optimized for smooth operation on various devices

### Color Scheme
- Primary: #007AFF (iOS Blue)
- Success: #28a745 (Green)
- Warning: #ffc107 (Amber)
- Danger: #dc3545 (Red)
- Background: #f5f5f5 (Light Gray)

## 📱 Device Requirements

### Minimum Requirements
- **Android**: API level 21 (Android 5.0) or higher
- **iOS**: iOS 11.0 or higher
- **Storage**: 100MB available space
- **RAM**: 2GB minimum (4GB recommended)

### Permissions
- **Storage**: Local data storage and backup creation
- **Phone**: Direct calling functionality
- **SMS**: Direct messaging capabilities
- **Notifications**: Debt reminder alerts

## 🐛 Troubleshooting

### Common Issues

1. **App won't start**
   - Ensure all dependencies are installed
   - Clear Metro cache: `npx react-native start --reset-cache`
   - Rebuild the app

2. **Data not persisting**
   - Check device storage availability
   - Verify app permissions
   - Restart the application

3. **Performance issues**
   - Clear app data and restart
   - Check available device memory
   - Update to latest app version

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with proper TypeScript typing
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👨‍💻 Author

**Samuel Kpassegna**
- Email: <EMAIL>
- Website: https://skpassegna.me
- Package: me.skpassegna.wholesale

## 📞 Support

For technical support or feature requests, please contact the development team or create an issue in the repository.

---

*Last updated: January 2025*