import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  FlatList,
  Modal,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Sale, SaleItem, Client, Product, SaleStackParamList } from '@/types';
import { formatCurrency, generateId } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type SaleFormProps = StackScreenProps<SaleStackParamList, 'SaleForm'>;
type SaleFormNavigationProp = StackNavigationProp<SaleStackParamList, 'SaleForm'>;

const SaleFormScreen: React.FC<SaleFormProps> = () => {
  const navigation = useNavigation<SaleFormNavigationProp>();
  const route = useRoute<SaleFormProps['route']>();
  const { saleId } = route.params;
  const isEditing = !!saleId;

  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [items, setItems] = useState<SaleItem[]>([]);
  const [receiptStatus, setReceiptStatus] = useState<'issued' | 'not_issued'>('not_issued');
  const [totalAmount, setTotalAmount] = useState(0);
  
  const [clients, setClients] = useState<Client[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  const [showClientModal, setShowClientModal] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [editingItemIndex, setEditingItemIndex] = useState<number | null>(null);
  
  const [clientSearchQuery, setClientSearchQuery] = useState('');
  const [productSearchQuery, setProductSearchQuery] = useState('');
  
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    // Calculate total amount whenever items change
    const total = items.reduce((sum, item) => sum + item.totalPrice, 0);
    setTotalAmount(total);
  }, [items]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      const [allClients, allProducts] = await Promise.all([
        db.getAllClients(),
        db.getAllProducts(),
      ]);
      
      setClients(allClients);
      setProducts(allProducts);
      
      if (isEditing) {
        const saleData = await db.getSaleById(saleId!);
        if (saleData) {
          setDate(new Date(saleData.date).toISOString().split('T')[0]);
          setItems(saleData.items);
          setReceiptStatus(saleData.receiptStatus);
          
          if (saleData.clientId) {
            const clientData = await db.getClientById(saleData.clientId);
            setSelectedClient(clientData);
          }
        }
      }
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};
    
    if (!date) {
      newErrors.date = 'Date is required';
    }
    
    if (items.length === 0) {
      newErrors.items = 'At least one item is required';
    }
    
    // Validate each item
    items.forEach((item, index) => {
      if (item.quantity <= 0) {
        newErrors[`item_${index}_quantity`] = 'Quantity must be greater than 0';
      }
      if (item.pricePerUnit <= 0) {
        newErrors[`item_${index}_price`] = 'Price must be greater than 0';
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please correct the errors and try again.');
      return;
    }
    
    try {
      setSaving(true);
      
      const saleData: Sale = {
        id: isEditing ? saleId! : generateId(),
        date: new Date(date),
        items,
        clientId: selectedClient?.id,
        receiptStatus,
        totalAmount,
        createdAt: isEditing ? (await db.getSaleById(saleId!))?.createdAt || new Date() : new Date(),
        updatedAt: new Date(),
      };
      
      if (isEditing) {
        await db.updateSale(saleData);
      } else {
        await db.createSale(saleData);
      }
      
      navigation.goBack();
    } catch (error) {
      console.error('Error saving sale:', error);
      Alert.alert('Error', 'Failed to save sale');
    } finally {
      setSaving(false);
    }
  };

  const addItem = (product: Product) => {
    const newItem: SaleItem = {
      productId: product.id,
      quantity: 1,
      pricePerUnit: 0, // User will enter this
      totalPrice: 0,
    };
    
    if (editingItemIndex !== null) {
      const updatedItems = [...items];
      updatedItems[editingItemIndex] = newItem;
      setItems(updatedItems);
      setEditingItemIndex(null);
    } else {
      setItems([...items, newItem]);
    }
    
    setShowProductModal(false);
    setProductSearchQuery('');
  };

  const updateItem = (index: number, field: keyof SaleItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Calculate total price when quantity or price changes
    if (field === 'quantity' || field === 'pricePerUnit') {
      updatedItems[index].totalPrice = updatedItems[index].quantity * updatedItems[index].pricePerUnit;
    }
    
    setItems(updatedItems);
    
    // Clear related errors
    if (field === 'quantity' || field === 'pricePerUnit') {
      const newErrors = { ...errors };
      delete newErrors[`item_${index}_${field === 'pricePerUnit' ? 'price' : field}`];
      setErrors(newErrors);
    }
  };

  const removeItem = (index: number) => {
    Alert.alert(
      'Remove Item',
      'Are you sure you want to remove this item?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            const updatedItems = items.filter((_, i) => i !== index);
            setItems(updatedItems);
          },
        },
      ]
    );
  };

  const getProductName = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'Unknown Product';
  };

  const getProductUnit = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.unitOfMeasure || '';
  };

  const filteredClients = clients.filter(client =>
    `${client.firstName} ${client.lastName || ''}`.toLowerCase().includes(clientSearchQuery.toLowerCase()) ||
    client.phoneNumber.includes(clientSearchQuery)
  );

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(productSearchQuery.toLowerCase())
  );

  const renderClientModal = () => (
    <Modal
      visible={showClientModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Select Customer</Text>
          <TouchableOpacity onPress={() => setShowClientModal(false)}>
            <Icon name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>
        
        <Input
          placeholder="Search customers..."
          value={clientSearchQuery}
          onChangeText={setClientSearchQuery}
          containerStyle={styles.searchContainer}
        />
        
        <TouchableOpacity
          style={styles.walkInOption}
          onPress={() => {
            setSelectedClient(null);
            setShowClientModal(false);
            setClientSearchQuery('');
          }}
        >
          <Icon name="person" size={24} color="#666" />
          <Text style={styles.walkInText}>Walk-in Customer</Text>
        </TouchableOpacity>
        
        <FlatList
          data={filteredClients}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.clientOption}
              onPress={() => {
                setSelectedClient(item);
                setShowClientModal(false);
                setClientSearchQuery('');
              }}
            >
              <Text style={styles.clientOptionName}>
                {item.firstName} {item.lastName || ''}
              </Text>
              <Text style={styles.clientOptionPhone}>{item.phoneNumber}</Text>
            </TouchableOpacity>
          )}
        />
      </SafeAreaView>
    </Modal>
  );

  const renderProductModal = () => (
    <Modal
      visible={showProductModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Select Product</Text>
          <TouchableOpacity onPress={() => {
            setShowProductModal(false);
            setEditingItemIndex(null);
            setProductSearchQuery('');
          }}>
            <Icon name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>
        
        <Input
          placeholder="Search products..."
          value={productSearchQuery}
          onChangeText={setProductSearchQuery}
          containerStyle={styles.searchContainer}
        />
        
        <FlatList
          data={filteredProducts}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.productOption}
              onPress={() => addItem(item)}
            >
              <Text style={styles.productOptionName}>{item.name}</Text>
              <Text style={styles.productOptionUnit}>Unit: {item.unitOfMeasure}</Text>
            </TouchableOpacity>
          )}
        />
      </SafeAreaView>
    </Modal>
  );

  const renderSaleItem = ({ item, index }: { item: SaleItem; index: number }) => (
    <Card style={styles.itemCard}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemNumber}>#{index + 1}</Text>
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => removeItem(index)}
        >
          <Icon name="close" size={20} color="#F44336" />
        </TouchableOpacity>
      </View>
      
      <TouchableOpacity
        style={styles.productSelector}
        onPress={() => {
          setEditingItemIndex(index);
          setShowProductModal(true);
        }}
      >
        <Text style={styles.productName}>{getProductName(item.productId)}</Text>
        <Icon name="keyboard-arrow-right" size={24} color="#666" />
      </TouchableOpacity>
      
      <View style={styles.itemInputs}>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Quantity ({getProductUnit(item.productId)})</Text>
          <Input
            value={item.quantity.toString()}
            onChangeText={(text) => updateItem(index, 'quantity', parseFloat(text) || 0)}
            keyboardType="numeric"
            containerStyle={styles.quantityInput}
            error={errors[`item_${index}_quantity`]}
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Price per Unit</Text>
          <Input
            value={item.pricePerUnit.toString()}
            onChangeText={(text) => updateItem(index, 'pricePerUnit', parseFloat(text) || 0)}
            keyboardType="numeric"
            containerStyle={styles.priceInput}
            error={errors[`item_${index}_price`]}
          />
        </View>
      </View>
      
      <View style={styles.itemTotal}>
        <Text style={styles.itemTotalLabel}>Total:</Text>
        <Text style={styles.itemTotalValue}>{formatCurrency(item.totalPrice)}</Text>
      </View>
    </Card>
  );

  if (loading) {
    return <LoadingSpinner text="Loading..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Sale Information */}
        <Card style={styles.infoCard}>
          <Text style={styles.sectionTitle}>Sale Information</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Date *</Text>
            <Input
              value={date}
              onChangeText={setDate}
              error={errors.date}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Customer</Text>
            <TouchableOpacity
              style={styles.clientSelector}
              onPress={() => setShowClientModal(true)}
            >
              <View style={styles.clientSelectorContent}>
                {selectedClient ? (
                  <View>
                    <Text style={styles.selectedClientName}>
                      {selectedClient.firstName} {selectedClient.lastName || ''}
                    </Text>
                    <Text style={styles.selectedClientPhone}>{selectedClient.phoneNumber}</Text>
                  </View>
                ) : (
                  <Text style={styles.clientPlaceholder}>Walk-in Customer (tap to select)</Text>
                )}
              </View>
              <Icon name="keyboard-arrow-right" size={24} color="#666" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Receipt Status</Text>
            <View style={styles.receiptButtons}>
              <TouchableOpacity
                style={[
                  styles.receiptButton,
                  receiptStatus === 'not_issued' && styles.receiptButtonActive
                ]}
                onPress={() => setReceiptStatus('not_issued')}
              >
                <Text style={[
                  styles.receiptButtonText,
                  receiptStatus === 'not_issued' && styles.receiptButtonTextActive
                ]}>No Receipt</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.receiptButton,
                  receiptStatus === 'issued' && styles.receiptButtonActive
                ]}
                onPress={() => setReceiptStatus('issued')}
              >
                <Text style={[
                  styles.receiptButtonText,
                  receiptStatus === 'issued' && styles.receiptButtonTextActive
                ]}>Receipt Issued</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Card>

        {/* Sale Items */}
        <Card style={styles.itemsCard}>
          <View style={styles.itemsHeader}>
            <Text style={styles.sectionTitle}>Items *</Text>
            <TouchableOpacity
              style={styles.addItemButton}
              onPress={() => setShowProductModal(true)}
            >
              <Icon name="add" size={20} color="#fff" />
              <Text style={styles.addItemText}>Add Item</Text>
            </TouchableOpacity>
          </View>
          
          {errors.items && (
            <Text style={styles.errorText}>{errors.items}</Text>
          )}
          
          <FlatList
            data={items}
            renderItem={renderSaleItem}
            keyExtractor={(_, index) => index.toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
          
          {/* Sale Total */}
          {items.length > 0 && (
            <View style={styles.totalContainer}>
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>Total Items:</Text>
                <Text style={styles.totalValue}>{items.length}</Text>
              </View>
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>Total Quantity:</Text>
                <Text style={styles.totalValue}>
                  {items.reduce((sum, item) => sum + item.quantity, 0)}
                </Text>
              </View>
              <View style={[styles.totalRow, styles.grandTotalRow]}>
                <Text style={styles.grandTotalLabel}>Grand Total:</Text>
                <Text style={styles.grandTotalValue}>{formatCurrency(totalAmount)}</Text>
              </View>
            </View>
          )}
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          title="Cancel"
          onPress={() => navigation.goBack()}
          style={styles.cancelButton}
          textStyle={styles.cancelButtonText}
        />
        <Button
          title={isEditing ? 'Update Sale' : 'Create Sale'}
          onPress={handleSave}
          style={styles.saveButton}
          textStyle={styles.saveButtonText}
          loading={saving}
        />
      </View>

      {renderClientModal()}
      {renderProductModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  infoCard: {
    margin: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 6,
  },
  clientSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  clientSelectorContent: {
    flex: 1,
  },
  selectedClientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  selectedClientPhone: {
    fontSize: 14,
    color: '#666',
  },
  clientPlaceholder: {
    fontSize: 16,
    color: '#999',
  },
  receiptButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  receiptButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  receiptButtonActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  receiptButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  receiptButtonTextActive: {
    color: '#fff',
  },
  itemsCard: {
    marginHorizontal: 16,
    marginBottom: 80,
  },
  itemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addItemButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  addItemText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginBottom: 8,
  },
  itemCard: {
    marginBottom: 12,
    backgroundColor: '#fafafa',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemNumber: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
    backgroundColor: '#e0e0e0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  removeButton: {
    padding: 4,
  },
  productSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 6,
    marginBottom: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  itemInputs: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  quantityInput: {
    flex: 1,
    marginBottom: 0,
  },
  priceInput: {
    flex: 1,
    marginBottom: 0,
  },
  itemTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  itemTotalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  itemTotalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  totalContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  totalLabel: {
    fontSize: 14,
    color: '#666',
  },
  totalValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  grandTotalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  grandTotalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  grandTotalValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#666',
  },
  saveButton: {
    flex: 2,
    backgroundColor: '#4CAF50',
  },
  saveButtonText: {
    color: '#fff',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  searchContainer: {
    margin: 16,
    marginBottom: 8,
  },
  walkInOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    gap: 12,
  },
  walkInText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  clientOption: {
    padding: 16,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  clientOptionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  clientOptionPhone: {
    fontSize: 14,
    color: '#666',
  },
  productOption: {
    padding: 16,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  productOptionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  productOptionUnit: {
    fontSize: 14,
    color: '#666',
  },
});

export default SaleFormScreen;