import PushNotification from 'react-native-push-notification';
import { Platform } from 'react-native';
import DatabaseService from './database';

class NotificationService {
  private static instance: NotificationService;
  private db: DatabaseService;

  constructor() {
    this.db = DatabaseService.getInstance();
    this.configure();
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  private configure(): void {
    PushNotification.configure({
      onNotification: function (notification) {
        console.log('Notification received:', notification);
      },
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },
      popInitialNotification: true,
      requestPermissions: Platform.OS === 'ios',
    });

    // Schedule daily debt reminder
    this.scheduleDailyDebtReminder();
  }

  private async scheduleDailyDebtReminder(): Promise<void> {
    // Cancel existing daily reminders
    PushNotification.cancelAllLocalNotifications();

    // Schedule for 5 PM daily
    const now = new Date();
    const reminderTime = new Date();
    reminderTime.setHours(17, 0, 0, 0); // 5:00 PM

    // If it's already past 5 PM today, schedule for tomorrow
    if (now.getTime() > reminderTime.getTime()) {
      reminderTime.setDate(reminderTime.getDate() + 1);
    }

    PushNotification.localNotificationSchedule({
      id: 'daily-debt-reminder',
      title: 'Outstanding Debts Reminder',
      message: 'You have outstanding debts to collect',
      date: reminderTime,
      repeatType: 'day',
      actions: ['View Debts'],
    });
  }

  async sendDebtReminder(): Promise<void> {
    try {
      const outstandingDebts = await this.db.getOutstandingDebts();
      
      if (outstandingDebts.length === 0) {
        return;
      }

      const totalAmount = outstandingDebts.reduce((sum, debt) => sum + debt.amount, 0);
      const debtorCount = outstandingDebts.length;

      let message = `You have ${debtorCount} outstanding debt${debtorCount > 1 ? 's' : ''} `;
      message += `totaling $${totalAmount.toFixed(2)}`;

      PushNotification.localNotification({
        id: 'debt-reminder',
        title: 'Debt Collection Reminder',
        message,
        playSound: true,
        soundName: 'default',
        actions: ['View Debts', 'Dismiss'],
      });
    } catch (error) {
      console.error('Error sending debt reminder:', error);
    }
  }

  scheduleCustomReminder(
    id: string,
    title: string,
    message: string,
    date: Date
  ): void {
    PushNotification.localNotificationSchedule({
      id,
      title,
      message,
      date,
      playSound: true,
      soundName: 'default',
    });
  }

  cancelReminder(id: string): void {
    PushNotification.cancelLocalNotifications({ id });
  }

  async checkAndSendDailyReminder(): Promise<void> {
    const now = new Date();
    const currentHour = now.getHours();
    
    // Check if it's 5 PM (17:00)
    if (currentHour === 17) {
      await this.sendDebtReminder();
    }
  }

  requestPermissions(): Promise<boolean> {
    return new Promise((resolve) => {
      PushNotification.requestPermissions((permissions) => {
        resolve(permissions.alert && permissions.sound);
      });
    });
  }

  async scheduleDeliveryReminder(
    acquisitionId: string,
    expectedDate: Date,
    providerName: string
  ): Promise<void> {
    const reminderDate = new Date(expectedDate.getTime() - 2 * 60 * 60 * 1000); // 2 hours before
    
    PushNotification.localNotificationSchedule({
      id: `delivery-${acquisitionId}`,
      title: 'Delivery Expected',
      message: `Delivery from ${providerName} expected in 2 hours`,
      date: reminderDate,
      playSound: true,
      soundName: 'default',
    });
  }

  cancelDeliveryReminder(acquisitionId: string): void {
    this.cancelReminder(`delivery-${acquisitionId}`);
  }
}

export default NotificationService;