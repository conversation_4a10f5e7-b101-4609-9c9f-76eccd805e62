import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Sale, Client, Product, SaleStackParamList } from '@/types';
import { formatDate, formatCurrency } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type SaleListNavigationProp = StackNavigationProp<SaleStackParamList, 'SaleList'>;

const SaleListScreen: React.FC = () => {
  const navigation = useNavigation<SaleListNavigationProp>();
  const [sales, setSales] = useState<Sale[]>([]);
  const [filteredSales, setFilteredSales] = useState<Sale[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useFocusEffect(
    useCallback(() => {
      loadSales();
    }, [])
  );

  const loadSales = async () => {
    try {
      setLoading(true);
      const [allSales, allClients, allProducts] = await Promise.all([
        db.getAllSales(),
        db.getAllClients(),
        db.getAllProducts(),
      ]);
      
      setSales(allSales);
      setClients(allClients);
      setProducts(allProducts);
      setFilteredSales(allSales);
    } catch (error) {
      console.error('Error loading sales:', error);
      Alert.alert('Error', 'Failed to load sales');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim() === '') {
      setFilteredSales(sales);
    } else {
      const filtered = sales.filter(sale => {
        const client = getClientName(sale.clientId);
        const products = sale.items.map(item => getProductName(item.productId)).join(' ');
        const dateStr = formatDate(new Date(sale.date));
        const amountStr = formatCurrency(sale.totalAmount);
        
        return (
          client.toLowerCase().includes(query.toLowerCase()) ||
          products.toLowerCase().includes(query.toLowerCase()) ||
          dateStr.toLowerCase().includes(query.toLowerCase()) ||
          amountStr.includes(query) ||
          sale.receiptStatus.toLowerCase().includes(query.toLowerCase())
        );
      });
      setFilteredSales(filtered);
    }
  };

  const handleDeleteSale = (sale: Sale) => {
    Alert.alert(
      'Delete Sale',
      `Are you sure you want to delete this sale of ${formatCurrency(sale.totalAmount)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteSale(sale.id);
              await loadSales();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete sale');
            }
          },
        },
      ]
    );
  };

  const getClientName = (clientId?: string): string => {
    if (!clientId) return 'Walk-in Customer';
    const client = clients.find(c => c.id === clientId);
    return client ? `${client.firstName} ${client.lastName || ''}`.trim() : 'Unknown Client';
  };

  const getProductName = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'Unknown Product';
  };

  const getReceiptStatusColor = (status: 'issued' | 'not_issued') => {
    return status === 'issued' ? '#4CAF50' : '#FF9800';
  };

  const getReceiptStatusIcon = (status: 'issued' | 'not_issued') => {
    return status === 'issued' ? 'receipt' : 'receipt-long';
  };

  const renderSale = ({ item }: { item: Sale }) => (
    <Card
      style={styles.saleCard}
      onPress={() => navigation.navigate('SaleDetails', { saleId: item.id })}
    >
      <View style={styles.saleHeader}>
        <View style={styles.saleInfo}>
          <Text style={styles.clientName}>{getClientName(item.clientId)}</Text>
          <Text style={styles.saleDate}>{formatDate(new Date(item.date))}</Text>
          <Text style={styles.itemCount}>
            {item.items.length} item{item.items.length !== 1 ? 's' : ''}
          </Text>
        </View>
        <View style={styles.saleActions}>
          <View style={styles.amountContainer}>
            <Text style={styles.saleAmount}>{formatCurrency(item.totalAmount)}</Text>
            <View style={styles.receiptStatus}>
              <Icon 
                name={getReceiptStatusIcon(item.receiptStatus)} 
                size={14} 
                color={getReceiptStatusColor(item.receiptStatus)} 
              />
              <Text style={[styles.receiptText, { color: getReceiptStatusColor(item.receiptStatus) }]}>
                {item.receiptStatus === 'issued' ? 'Receipt Issued' : 'No Receipt'}
              </Text>
            </View>
          </View>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('SaleForm', { saleId: item.id })}
            >
              <Icon name="edit" size={20} color="#FF9800" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteSale(item)}
            >
              <Icon name="delete" size={20} color="#F44336" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      {/* Product Summary */}
      <View style={styles.productSummary}>
        {item.items.slice(0, 3).map((saleItem, index) => (
          <Text key={index} style={styles.productItem}>
            {getProductName(saleItem.productId)} (×{saleItem.quantity})
          </Text>
        ))}
        {item.items.length > 3 && (
          <Text style={styles.moreItems}>+{item.items.length - 3} more items</Text>
        )}
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="point-of-sale" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>No Sales Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? 'No sales match your search criteria.'
          : 'Record your first sale to get started.'
        }
      </Text>
    </View>
  );

  const calculateTotalSales = () => {
    return filteredSales.reduce((sum, sale) => sum + sale.totalAmount, 0);
  };

  if (loading) {
    return <LoadingSpinner text="Loading sales..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Input
          placeholder="Search sales..."
          value={searchQuery}
          onChangeText={handleSearch}
          containerStyle={styles.searchContainer}
          inputStyle={styles.searchInput}
        />
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('SaleForm', {})}
        >
          <Icon name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.summaryInfo}>
        <View style={styles.resultInfo}>
          <Text style={styles.resultCount}>
            {filteredSales.length} sale{filteredSales.length !== 1 ? 's' : ''}
            {searchQuery && ` found for "${searchQuery}"`}
          </Text>
        </View>
        <View style={styles.totalSales}>
          <Text style={styles.totalSalesText}>Total: {formatCurrency(calculateTotalSales())}</Text>
        </View>
      </View>

      <FlatList
        data={filteredSales.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())}
        renderItem={renderSale}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  searchContainer: {
    flex: 1,
    marginBottom: 0,
    marginRight: 12,
  },
  searchInput: {
    borderRadius: 25,
    paddingHorizontal: 16,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  summaryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  resultInfo: {
    flex: 1,
  },
  resultCount: {
    fontSize: 14,
    color: '#666',
  },
  totalSales: {
    alignItems: 'flex-end',
  },
  totalSalesText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  listContent: {
    padding: 8,
    flexGrow: 1,
  },
  saleCard: {
    marginBottom: 8,
  },
  saleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  saleInfo: {
    flex: 1,
    marginRight: 12,
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  saleDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  itemCount: {
    fontSize: 12,
    color: '#999',
  },
  saleActions: {
    alignItems: 'flex-end',
  },
  amountContainer: {
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  saleAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  receiptStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  receiptText: {
    fontSize: 11,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  productSummary: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  productItem: {
    fontSize: 13,
    color: '#666',
    marginBottom: 2,
  },
  moreItems: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default SaleListScreen;