import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Provider, ProviderStackParamList } from '@/types';
import { makePhoneCall, openWhatsApp } from '@/utils/helpers';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ProviderListNavigationProp = StackNavigationProp<ProviderStackParamList, 'ProviderList'>;

const ProviderListScreen: React.FC = () => {
  const navigation = useNavigation<ProviderListNavigationProp>();
  const [providers, setProviders] = useState<Provider[]>([]);
  const [filteredProviders, setFilteredProviders] = useState<Provider[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useFocusEffect(
    useCallback(() => {
      loadProviders();
    }, [])
  );

  const loadProviders = async () => {
    try {
      setLoading(true);
      const allProviders = await db.getAllProviders();
      setProviders(allProviders);
      setFilteredProviders(allProviders);
    } catch (error) {
      console.error('Error loading providers:', error);
      Alert.alert('Error', 'Failed to load providers');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim() === '') {
      setFilteredProviders(providers);
    } else {
      const filtered = providers.filter(provider =>
        provider.firstName.toLowerCase().includes(query.toLowerCase()) ||
        provider.lastName?.toLowerCase().includes(query.toLowerCase()) ||
        provider.companyName?.toLowerCase().includes(query.toLowerCase()) ||
        provider.phoneNumbers.some(phone => phone.number.includes(query))
      );
      setFilteredProviders(filtered);
    }
  };

  const handleDeleteProvider = (provider: Provider) => {
    Alert.alert(
      'Delete Provider',
      `Are you sure you want to delete "${provider.firstName} ${provider.lastName || ''}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.deleteProvider(provider.id);
              await loadProviders();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete provider');
            }
          },
        },
      ]
    );
  };

  const handleContactProvider = (provider: Provider, method: 'call' | 'whatsapp') => {
    if (provider.phoneNumbers.length === 0) {
      Alert.alert('Error', 'No phone number available');
      return;
    }

    const primaryPhone = provider.phoneNumbers.find(p => p.isPrimary) || provider.phoneNumbers[0];
    
    if (method === 'call') {
      makePhoneCall(primaryPhone.number);
    } else if (method === 'whatsapp' && primaryPhone.hasWhatsApp) {
      openWhatsApp(primaryPhone.number);
    } else {
      Alert.alert('Error', 'WhatsApp not available for this number');
    }
  };

  const renderProvider = ({ item }: { item: Provider }) => (
    <Card
      style={styles.providerCard}
      onPress={() => navigation.navigate('ProviderDetails', { providerId: item.id })}
    >
      <View style={styles.providerHeader}>
        <View style={styles.providerInfo}>
          <Text style={styles.providerName}>
            {item.firstName} {item.lastName || ''}
          </Text>
          {item.companyName && (
            <Text style={styles.companyName}>{item.companyName}</Text>
          )}
          {item.phoneNumbers.length > 0 && (
            <Text style={styles.phoneNumber}>
              {item.phoneNumbers[0].number}
            </Text>
          )}
          {item.email && (
            <Text style={styles.email}>{item.email}</Text>
          )}
        </View>
        <View style={styles.providerActions}>
          {item.phoneNumbers.length > 0 && (
            <>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleContactProvider(item, 'call')}
              >
                <Icon name="phone" size={20} color="#4CAF50" />
              </TouchableOpacity>
              {item.phoneNumbers.some(p => p.hasWhatsApp) && (
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleContactProvider(item, 'whatsapp')}
                >
                  <Icon name="chat" size={20} color="#25D366" />
                </TouchableOpacity>
              )}
            </>
          )}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('ProviderForm', { providerId: item.id })}
          >
            <Icon name="edit" size={20} color="#2196F3" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteProvider(item)}
          >
            <Icon name="delete" size={20} color="#F44336" />
          </TouchableOpacity>
        </View>
      </View>
      
      {item.notes && (
        <Text style={styles.notes} numberOfLines={2}>
          {item.notes}
        </Text>
      )}
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="business" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>No Providers Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? 'No providers match your search criteria.'
          : 'Add your first provider to get started.'
        }
      </Text>
    </View>
  );

  if (loading) {
    return <LoadingSpinner text="Loading providers..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Input
          placeholder="Search providers..."
          value={searchQuery}
          onChangeText={handleSearch}
          containerStyle={styles.searchContainer}
          inputStyle={styles.searchInput}
        />
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('ProviderForm', {})}
        >
          <Icon name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.resultInfo}>
        <Text style={styles.resultCount}>
          {filteredProviders.length} provider{filteredProviders.length !== 1 ? 's' : ''}
          {searchQuery && ` found for "${searchQuery}"`}
        </Text>
      </View>

      <FlatList
        data={filteredProviders}
        renderItem={renderProvider}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  searchContainer: {
    flex: 1,
    marginBottom: 0,
    marginRight: 12,
  },
  searchInput: {
    borderRadius: 25,
    paddingHorizontal: 16,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  resultInfo: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  resultCount: {
    fontSize: 14,
    color: '#666',
  },
  listContent: {
    padding: 8,
    flexGrow: 1,
  },
  providerCard: {
    marginBottom: 8,
  },
  providerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  providerInfo: {
    flex: 1,
    marginRight: 12,
  },
  providerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  companyName: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  phoneNumber: {
    fontSize: 14,
    color: '#2196F3',
    marginBottom: 2,
  },
  email: {
    fontSize: 14,
    color: '#666',
  },
  providerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  notes: {
    fontSize: 12,
    color: '#999',
    marginTop: 8,
    lineHeight: 16,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default ProviderListScreen;