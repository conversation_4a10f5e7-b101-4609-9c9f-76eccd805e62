{"name": "wholesale-shop-management", "version": "1.0.0", "description": "Offline Wholesale Shop Management Application", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "18.2.0", "react-native": "0.73.4", "react-native-async-storage": "^0.0.1", "react-native-contacts": "^7.0.8", "react-native-date-picker": "^4.4.2", "react-native-gesture-handler": "^2.14.1", "react-native-image-picker": "^7.1.0", "react-native-paper": "^5.12.3", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.6.2", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-sqlite-storage": "^6.0.1", "react-native-vector-icons": "^10.0.3", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-share": "^10.0.2", "react-native-fs": "^2.20.0", "react-native-permissions": "^4.1.5", "@react-native-picker/picker": "^2.6.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.3", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.77.0", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}