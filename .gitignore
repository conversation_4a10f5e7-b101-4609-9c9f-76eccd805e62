# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Expo
.expo/
web-build/

# Flipper
ios/Flipper

# ignore for watchman
.watchmanconfig

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# testing
/coverage

# Turborepo
.turbo

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ruby / fastlane
/vendor/bundle/
/ios/vendor/bundle/