import {
  generateId,
  formatCurrency,
  formatDate,
  validateEmail,
  validatePhoneNumber,
  fuzzySearch,
  isToday,
  isOverdue,
  getDaysUntil,
  roundToTwo,
  calculatePercentage,
  capitalizeFirstLetter,
  getInitials,
} from '../helpers';

describe('Helper Functions', () => {
  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(0);
    });
  });

  describe('formatCurrency', () => {
    it('should format currency with default USD', () => {
      expect(formatCurrency(1234.56)).toBe('$1,234.56');
    });

    it('should format currency with specified currency', () => {
      expect(formatCurrency(1234.56, 'EUR')).toBe('€1,234.56');
    });

    it('should handle zero amount', () => {
      expect(formatCurrency(0)).toBe('$0.00');
    });
  });

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = new Date('2024-01-15');
      const formatted = formatDate(date);
      
      expect(formatted).toMatch(/Jan 15, 2024/);
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@domain.com')).toBe(false);
    });
  });

  describe('validatePhoneNumber', () => {
    it('should validate correct phone numbers', () => {
      expect(validatePhoneNumber('+1234567890')).toBe(true);
      expect(validatePhoneNumber('1234567890')).toBe(true);
    });

    it('should reject invalid phone numbers', () => {
      expect(validatePhoneNumber('123')).toBe(false);
      expect(validatePhoneNumber('abc')).toBe(false);
      expect(validatePhoneNumber('')).toBe(false);
    });
  });

  describe('fuzzySearch', () => {
    it('should find exact matches', () => {
      expect(fuzzySearch('test', 'test string')).toBe(true);
    });

    it('should find partial matches', () => {
      expect(fuzzySearch('str', 'test string')).toBe(true);
    });

    it('should handle character order matches', () => {
      expect(fuzzySearch('tst', 'test string')).toBe(true);
    });

    it('should be case insensitive', () => {
      expect(fuzzySearch('TEST', 'test string')).toBe(true);
    });

    it('should return false for no matches', () => {
      expect(fuzzySearch('xyz', 'test string')).toBe(false);
    });
  });

  describe('Date utilities', () => {
    describe('isToday', () => {
      it('should return true for today', () => {
        const today = new Date();
        expect(isToday(today)).toBe(true);
      });

      it('should return false for yesterday', () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        expect(isToday(yesterday)).toBe(false);
      });
    });

    describe('isOverdue', () => {
      it('should return true for past dates', () => {
        const pastDate = new Date();
        pastDate.setDate(pastDate.getDate() - 1);
        expect(isOverdue(pastDate)).toBe(true);
      });

      it('should return false for future dates', () => {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 1);
        expect(isOverdue(futureDate)).toBe(false);
      });
    });

    describe('getDaysUntil', () => {
      it('should calculate days until future date', () => {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 5);
        expect(getDaysUntil(futureDate)).toBe(5);
      });

      it('should return negative for past dates', () => {
        const pastDate = new Date();
        pastDate.setDate(pastDate.getDate() - 3);
        expect(getDaysUntil(pastDate)).toBe(-3);
      });
    });
  });

  describe('Number utilities', () => {
    describe('roundToTwo', () => {
      it('should round to two decimal places', () => {
        expect(roundToTwo(1.234567)).toBe(1.23);
        expect(roundToTwo(1.999)).toBe(2);
        expect(roundToTwo(1)).toBe(1);
      });
    });

    describe('calculatePercentage', () => {
      it('should calculate percentage correctly', () => {
        expect(calculatePercentage(25, 100)).toBe(25);
        expect(calculatePercentage(1, 3)).toBe(33.33);
      });

      it('should handle zero total', () => {
        expect(calculatePercentage(10, 0)).toBe(0);
      });
    });
  });

  describe('Text utilities', () => {
    describe('capitalizeFirstLetter', () => {
      it('should capitalize first letter', () => {
        expect(capitalizeFirstLetter('hello')).toBe('Hello');
        expect(capitalizeFirstLetter('HELLO')).toBe('HELLO');
        expect(capitalizeFirstLetter('')).toBe('');
      });
    });

    describe('getInitials', () => {
      it('should get initials from names', () => {
        expect(getInitials('John', 'Doe')).toBe('JD');
        expect(getInitials('Jane')).toBe('J');
        expect(getInitials('', 'Smith')).toBe('S');
      });
    });
  });
});