import DatabaseService from '../database';
import { Product, Provider } from '@/types';

// Mock AsyncStorage
const mockAsyncStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

describe('DatabaseService', () => {
  let db: DatabaseService;

  beforeEach(() => {
    db = DatabaseService.getInstance();
    jest.clearAllMocks();
  });

  describe('Product Operations', () => {
    const mockProduct: Product = {
      id: '1',
      name: 'Test Product',
      salableIndividually: true,
      wholesaleQuantity: '10kg',
      unitOfMeasure: 'kg',
      providers: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should create a new product', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('[]');
      mockAsyncStorage.setItem.mockResolvedValue(undefined);

      const result = await db.createProduct(mockProduct);

      expect(result).toEqual(mockProduct);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'products',
        JSON.stringify([mockProduct])
      );
    });

    it('should retrieve all products', async () => {
      const products = [mockProduct];
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(products));

      const result = await db.getAllProducts();

      expect(result).toEqual(products);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('products');
    });

    it('should retrieve product by id', async () => {
      const products = [mockProduct];
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(products));

      const result = await db.getProductById('1');

      expect(result).toEqual(mockProduct);
    });

    it('should update existing product', async () => {
      const products = [mockProduct];
      const updatedProduct = { ...mockProduct, name: 'Updated Product' };
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(products));
      mockAsyncStorage.setItem.mockResolvedValue(undefined);

      const result = await db.updateProduct(updatedProduct);

      expect(result).toEqual(updatedProduct);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'products',
        JSON.stringify([updatedProduct])
      );
    });

    it('should delete product', async () => {
      const products = [mockProduct];
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(products));
      mockAsyncStorage.setItem.mockResolvedValue(undefined);

      const result = await db.deleteProduct('1');

      expect(result).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'products',
        JSON.stringify([])
      );
    });

    it('should search products by name', async () => {
      const products = [
        mockProduct,
        { ...mockProduct, id: '2', name: 'Another Product' },
      ];
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(products));

      const result = await db.searchProducts('Test');

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Test Product');
    });
  });

  describe('Provider Operations', () => {
    const mockProvider: Provider = {
      id: '1',
      firstName: 'John',
      lastName: 'Doe',
      phoneNumbers: [
        { number: '+**********', hasWhatsApp: true, isPrimary: true },
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should create a new provider', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('[]');
      mockAsyncStorage.setItem.mockResolvedValue(undefined);

      const result = await db.createProvider(mockProvider);

      expect(result).toEqual(mockProvider);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'providers',
        JSON.stringify([mockProvider])
      );
    });

    it('should search providers by name', async () => {
      const providers = [
        mockProvider,
        { ...mockProvider, id: '2', firstName: 'Jane' },
      ];
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(providers));

      const result = await db.searchProviders('John');

      expect(result).toHaveLength(1);
      expect(result[0].firstName).toBe('John');
    });
  });

  describe('Error Handling', () => {
    it('should handle AsyncStorage errors gracefully', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const result = await db.getAllProducts();

      expect(result).toEqual([]);
    });

    it('should return null for non-existent product', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('[]');

      const result = await db.getProductById('non-existent');

      expect(result).toBeNull();
    });
  });

  describe('Database Initialization', () => {
    it('should initialize database with mock data on first launch', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);
      mockAsyncStorage.setItem.mockResolvedValue(undefined);

      await db.initializeDatabase();

      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('firstLaunch', 'false');
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'products',
        expect.any(String)
      );
    });

    it('should not reinitialize if already initialized', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('false');

      await db.initializeDatabase();

      expect(mockAsyncStorage.setItem).not.toHaveBeenCalledWith(
        'products',
        expect.any(String)
      );
    });
  });
});