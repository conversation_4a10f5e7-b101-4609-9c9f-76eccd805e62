import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatabaseService from '@/services/database';
import { Provider, PhoneNumber, ProviderStackParamList } from '@/types';
import { generateId, validateEmail, validatePhoneNumber } from '@/utils/helpers';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type ProviderFormProps = StackScreenProps<ProviderStackParamList, 'ProviderForm'>;

interface ProviderForm {
  firstName: string;
  lastName?: string;
  phoneNumbers: PhoneNumber[];
  email?: string;
  photo?: string;
  website?: string;
  companyName?: string;
  address?: string;
  notes?: string;
}

const ProviderFormScreen: React.FC<ProviderFormProps> = () => {
  const navigation = useNavigation();
  const route = useRoute<ProviderFormProps['route']>();
  const { providerId } = route.params || {};

  const isEditing = !!providerId;

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [form, setForm] = useState<ProviderForm>({
    firstName: '',
    phoneNumbers: [{ number: '', hasWhatsApp: false, isPrimary: true }],
  });
  const [errors, setErrors] = useState<Partial<ProviderForm>>({});

  const db = DatabaseService.getInstance();

  useEffect(() => {
    if (isEditing) {
      loadProviderData();
    }
  }, []);

  const loadProviderData = async () => {
    try {
      setLoading(true);
      const providerData = await db.getProviderById(providerId!);
      
      if (providerData) {
        setForm({
          firstName: providerData.firstName,
          lastName: providerData.lastName,
          phoneNumbers: providerData.phoneNumbers.length > 0 
            ? providerData.phoneNumbers 
            : [{ number: '', hasWhatsApp: false, isPrimary: true }],
          email: providerData.email,
          photo: providerData.photo,
          website: providerData.website,
          companyName: providerData.companyName,
          address: providerData.address,
          notes: providerData.notes,
        });
      }
    } catch (error) {
      console.error('Error loading provider data:', error);
      Alert.alert('Error', 'Failed to load provider data');
    } finally {
      setLoading(false);
    }
  };

  const updateForm = <K extends keyof ProviderForm>(field: K, value: ProviderForm[K]) => {
    setForm(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const addPhoneNumber = () => {
    const newPhone: PhoneNumber = {
      number: '',
      hasWhatsApp: false,
      isPrimary: form.phoneNumbers.length === 0,
    };
    updateForm('phoneNumbers', [...form.phoneNumbers, newPhone]);
  };

  const removePhoneNumber = (index: number) => {
    if (form.phoneNumbers.length <= 1) {
      Alert.alert('Error', 'At least one phone number is required');
      return;
    }
    
    const updatedPhones = form.phoneNumbers.filter((_, i) => i !== index);
    
    // If we removed the primary phone, make the first one primary
    if (form.phoneNumbers[index].isPrimary && updatedPhones.length > 0) {
      updatedPhones[0].isPrimary = true;
    }
    
    updateForm('phoneNumbers', updatedPhones);
  };

  const updatePhoneNumber = (index: number, field: keyof PhoneNumber, value: any) => {
    const updatedPhones = [...form.phoneNumbers];
    
    if (field === 'isPrimary' && value) {
      // Make all others non-primary
      updatedPhones.forEach((phone, i) => {
        phone.isPrimary = i === index;
      });
    } else {
      updatedPhones[index] = { ...updatedPhones[index], [field]: value };
    }
    
    updateForm('phoneNumbers', updatedPhones);
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ProviderForm> = {};

    if (!form.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    // Validate phone numbers
    const validPhones = form.phoneNumbers.filter(phone => phone.number.trim());
    if (validPhones.length === 0) {
      Alert.alert('Error', 'At least one phone number is required');
      return false;
    }

    for (const phone of validPhones) {
      if (!validatePhoneNumber(phone.number)) {
        Alert.alert('Error', `Invalid phone number: ${phone.number}`);
        return false;
      }
    }

    // Validate email if provided
    if (form.email && !validateEmail(form.email)) {
      newErrors.email = 'Invalid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);

      // Filter out empty phone numbers
      const validPhoneNumbers = form.phoneNumbers.filter(phone => phone.number.trim());
      
      // Ensure at least one primary phone
      if (!validPhoneNumbers.some(phone => phone.isPrimary)) {
        validPhoneNumbers[0].isPrimary = true;
      }

      const providerData: Provider = {
        id: isEditing ? providerId! : generateId(),
        firstName: form.firstName.trim(),
        lastName: form.lastName?.trim(),
        phoneNumbers: validPhoneNumbers,
        email: form.email?.trim(),
        photo: form.photo,
        website: form.website?.trim(),
        companyName: form.companyName?.trim(),
        address: form.address?.trim(),
        notes: form.notes?.trim(),
        createdAt: isEditing ? new Date() : new Date(),
        updatedAt: new Date(),
      };

      if (isEditing) {
        await db.updateProvider(providerData);
      } else {
        await db.createProvider(providerData);
      }

      Alert.alert(
        'Success',
        `Provider ${isEditing ? 'updated' : 'created'} successfully`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error saving provider:', error);
      Alert.alert('Error', 'Failed to save provider');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="Loading..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          <Input
            label="First Name"
            value={form.firstName}
            onChangeText={(value) => updateForm('firstName', value)}
            error={errors.firstName}
            placeholder="Enter first name"
            required
          />

          <Input
            label="Last Name"
            value={form.lastName}
            onChangeText={(value) => updateForm('lastName', value)}
            placeholder="Enter last name"
          />

          <Input
            label="Company Name"
            value={form.companyName}
            onChangeText={(value) => updateForm('companyName', value)}
            placeholder="Enter company name"
          />

          <Input
            label="Address"
            value={form.address}
            onChangeText={(value) => updateForm('address', value)}
            placeholder="Enter address"
            multiline
            numberOfLines={3}
          />
        </Card>

        {/* Phone Numbers */}
        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Phone Numbers</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={addPhoneNumber}
            >
              <Icon name="add" size={20} color="#4CAF50" />
              <Text style={styles.addButtonText}>Add Phone</Text>
            </TouchableOpacity>
          </View>
          
          {form.phoneNumbers.map((phone, index) => (
            <View key={index} style={styles.phoneContainer}>
              <Input
                label={`Phone ${index + 1}`}
                value={phone.number}
                onChangeText={(value) => updatePhoneNumber(index, 'number', value)}
                placeholder="Enter phone number"
                keyboardType="phone-pad"
                containerStyle={styles.phoneInput}
              />
              
              <View style={styles.phoneOptions}>
                <View style={styles.switchContainer}>
                  <Text style={styles.switchLabel}>Primary</Text>
                  <Switch
                    value={phone.isPrimary}
                    onValueChange={(value) => updatePhoneNumber(index, 'isPrimary', value)}
                    trackColor={{ false: '#ccc', true: '#2196F3' }}
                    thumbColor={phone.isPrimary ? '#fff' : '#f4f3f4'}
                  />
                </View>
                
                <View style={styles.switchContainer}>
                  <Text style={styles.switchLabel}>WhatsApp</Text>
                  <Switch
                    value={phone.hasWhatsApp}
                    onValueChange={(value) => updatePhoneNumber(index, 'hasWhatsApp', value)}
                    trackColor={{ false: '#ccc', true: '#25D366' }}
                    thumbColor={phone.hasWhatsApp ? '#fff' : '#f4f3f4'}
                  />
                </View>
                
                {form.phoneNumbers.length > 1 && (
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removePhoneNumber(index)}
                  >
                    <Icon name="delete" size={20} color="#F44336" />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          ))}
        </Card>

        {/* Contact Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Additional Contact Info</Text>
          
          <Input
            label="Email Address"
            value={form.email}
            onChangeText={(value) => updateForm('email', value)}
            error={errors.email}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <Input
            label="Website"
            value={form.website}
            onChangeText={(value) => updateForm('website', value)}
            placeholder="Enter website URL"
            autoCapitalize="none"
          />
        </Card>

        {/* Notes */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          
          <Input
            label="Notes"
            value={form.notes}
            onChangeText={(value) => updateForm('notes', value)}
            placeholder="Enter any notes or additional information"
            multiline
            numberOfLines={4}
          />
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Cancel"
            onPress={() => navigation.goBack()}
            variant="secondary"
            style={styles.actionButton}
          />
          <Button
            title={isEditing ? 'Update Provider' : 'Create Provider'}
            onPress={handleSave}
            loading={saving}
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  phoneContainer: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  phoneInput: {
    marginBottom: 8,
  },
  phoneOptions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
  },
  removeButton: {
    padding: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default ProviderFormScreen;